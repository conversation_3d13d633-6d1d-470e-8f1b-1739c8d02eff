import { AxiosError } from 'axios';
import express, { type Response } from 'express';
import HttpApiError from '../utils/http_api_error';
import { getGenericErrorResponse } from '../utils';
import {
  createOrUpdateClinic,
  deleteUserClinicRole,
  getClinicOwners,
  getUserClinicDetails,
  getUserClinics,
  updateUserClinicRole,
} from '../controllers/clinic_controller';
import { IClinic } from '../models/Clinic';
import { IApiRequest } from '../types';
import { IUserInvite } from '../models/UserInvite';
import {
  acceptUserInvite,
  createUserInvite,
  getUserInvite,
} from '../controllers/user_invite_controller';
import { listAllCallsForAClinic } from '../controllers/retell_call_controller';
import { IUserClinicRole } from '../models/UserClinicRole';

const router = express.Router();

router.post('/clinics', (req: IApiRequest, res: Response) => {
  if (!req.user) {
    res.status(401).json({ message: 'Unauthorized access' });
    return;
  }
  const body = req.body as Partial<IClinic>;

  createOrUpdateClinic(req.user, body)
    .then((data) => {
      res.json({ data });
    })
    .catch((error: Error) => {
      const statusCode =
        (error as AxiosError).response?.status ??
        (error as HttpApiError).statusCode ??
        500;
      res.status(statusCode);
      const fallbackErrorResponse = getGenericErrorResponse();
      fallbackErrorResponse.error.message = error.message;
      res.json((error as AxiosError).response?.data ?? fallbackErrorResponse);
    });
});

router.get('/clinics/:id', (req: IApiRequest, res: Response) => {
  if (!req.user) {
    res.status(401).json({ message: 'Unauthorized access' });
    return;
  }

  const { id } = req.params;

  // Validate input
  if (!id) {
    res.status(400).json({
      error: {
        message: 'Mising clinic ID',
      },
    });
    return;
  }

  getUserClinicDetails(req.user, {
    clinicId: id as string,
  })
    .then((data) => {
      res.json({ data });
    })
    .catch((error: Error) => {
      const statusCode = (error as AxiosError).response?.status ?? 500;
      res.status(statusCode);
      const fallbackErrorResponse = getGenericErrorResponse();
      fallbackErrorResponse.error.message = error.message;
      res.json((error as AxiosError).response?.data ?? fallbackErrorResponse);
    });
});

router.get('/clinics', (req: IApiRequest, res: Response) => {
  if (!req.user) {
    res.status(401).json({ message: 'Unauthorized access' });
    return;
  }
  getUserClinics(req.user, {})
    .then((data) => {
      res.json({ data });
    })
    .catch((error: Error) => {
      const statusCode = (error as AxiosError).response?.status ?? 500; // Default to 500 if not available
      res.status(statusCode);
      const fallbackErrorResponse = getGenericErrorResponse();
      fallbackErrorResponse.error.message = error.message;
      res.json((error as AxiosError).response?.data ?? fallbackErrorResponse);
    });
});

router.post('/user-invites', (req: IApiRequest, res: Response) => {
  if (!req.user) {
    res.status(401).json({ message: 'Unauthorized access' });
    return;
  }
  const body = req.body as Partial<IUserInvite>;

  createUserInvite(req.user, body)
    .then((data) => {
      res.json({ data });
    })
    .catch((error: Error) => {
      const statusCode =
        (error as AxiosError).response?.status ??
        (error as HttpApiError).statusCode ??
        500;
      res.status(statusCode);
      const fallbackErrorResponse = getGenericErrorResponse();
      fallbackErrorResponse.error.message = error.message;
      res.json((error as AxiosError).response?.data ?? fallbackErrorResponse);
    });
});

router.get('/user-invites', (req: IApiRequest, res: Response) => {
  if (!req.user) {
    res.status(401).json({ message: 'Unauthorized access' });
    return;
  }
  const payload = {
    token: req.query?.token as string,
  };

  getUserInvite(req.user, payload)
    .then((data) => {
      res.json({ data });
    })
    .catch((error: Error) => {
      const statusCode =
        (error as AxiosError).response?.status ??
        (error as HttpApiError).statusCode ??
        500;
      res.status(statusCode);
      const fallbackErrorResponse = getGenericErrorResponse();
      fallbackErrorResponse.error.message = error.message;
      res.json((error as AxiosError).response?.data ?? fallbackErrorResponse);
    });
});

router.post('/user-invites/accept', (req: IApiRequest, res: Response) => {
  if (!req.user) {
    res.status(401).json({ message: 'Unauthorized access' });
    return;
  }
  const body = req.body as {
    token: string;
  };

  acceptUserInvite(req.user, body)
    .then((data) => {
      res.json({ data });
    })
    .catch((error: Error) => {
      const statusCode =
        (error as AxiosError).response?.status ??
        (error as HttpApiError).statusCode ??
        500;
      res.status(statusCode);
      const fallbackErrorResponse = getGenericErrorResponse();
      fallbackErrorResponse.error.message = error.message;
      res.json((error as AxiosError).response?.data ?? fallbackErrorResponse);
    });
});

router.get('/analytics/list-calls', (req: IApiRequest, res: Response) => {
  const clinicId = req.query.clinic_id as string;
  if (!clinicId) {
    res.status(400).json({
      error: {
        message: 'clinic id must be provided',
      },
    });
    return;
  }

  if (!req.user) {
    res.status(401).json({ message: 'Unauthorized access' });
    return;
  }
  listAllCallsForAClinic(req.user, {
    clinicId: clinicId as string,
    startDate: req.query.start_date as string,
    endDate: req.query.end_date as string,
  })
    .then((data) => {
      res.json({ data });
    })
    .catch((error: Error) => {
      const statusCode =
        (error as AxiosError).response?.status ??
        (error as HttpApiError).statusCode ??
        500;
      res.status(statusCode);
      const fallbackErrorResponse = getGenericErrorResponse();
      fallbackErrorResponse.error.message = error.message;
      res.json((error as AxiosError).response?.data ?? fallbackErrorResponse);
    });
});

router.get('/clinic-owners', (req: IApiRequest, res: Response) => {
  const clinicId = req.query.clinic_id as string;
  if (!clinicId) {
    res.status(400).json({
      error: {
        message: 'clinic id must be provided',
      },
    });
    return;
  }

  if (!req.user) {
    res.status(401).json({ message: 'Unauthorized access' });
    return;
  }
  getClinicOwners(req.user, clinicId)
    .then((data) => {
      res.json({ data });
    })
    .catch((error: Error) => {
      const statusCode =
        (error as AxiosError).response?.status ??
        (error as HttpApiError).statusCode ??
        500;
      res.status(statusCode);
      const fallbackErrorResponse = getGenericErrorResponse();
      fallbackErrorResponse.error.message = error.message;
      res.json((error as AxiosError).response?.data ?? fallbackErrorResponse);
    });
});

router.post('/user-clinic-roles', (req: IApiRequest, res: Response) => {
  const payload: IUserClinicRole = req.body;
  if (!req.user) {
    res.status(401).json({ message: 'Unauthorized access' });
    return;
  }
  if (!payload?.clinic_id || !payload?.user_id) {
    res.status(400).json({ message: 'Missing clinic_id or user_id in body' });
    return;
  }
  updateUserClinicRole(req.user, payload)
    .then((data) => {
      res.json({ data });
    })
    .catch((error: Error) => {
      const statusCode =
        (error as AxiosError).response?.status ??
        (error as HttpApiError).statusCode ??
        500;
      res.status(statusCode);
      const fallbackErrorResponse = getGenericErrorResponse();
      fallbackErrorResponse.error.message = error.message;
      res.json((error as AxiosError).response?.data ?? fallbackErrorResponse);
    });
});

router.delete('/user-clinic-roles', (req: IApiRequest, res: Response) => {
  const { clinic_id, user_id } = req.query;

  if (!req.user) {
    res.status(401).json({ message: 'Unauthorized access' });
    return;
  }

  if (!clinic_id || !user_id) {
    res
      .status(400)
      .json({ message: 'Missing clinic_id or user_id in query params' });
    return;
  }

  deleteUserClinicRole(req.user, {
    clinic_id: clinic_id as string,
    user_id: user_id as string,
  })
    .then((data) => {
      res.json({ data });
    })
    .catch((error: Error) => {
      const statusCode =
        (error as AxiosError).response?.status ??
        (error as HttpApiError).statusCode ??
        500;
      res.status(statusCode);
      const fallbackErrorResponse = getGenericErrorResponse();
      fallbackErrorResponse.error.message = error.message;
      res.json((error as AxiosError).response?.data ?? fallbackErrorResponse);
    });
});

export default router;

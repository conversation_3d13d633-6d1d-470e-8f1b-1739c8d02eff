import mongoose, { Schema, Document } from 'mongoose';

export enum RetellCallIntent {
  book_appointment,
  cancel_appointment,
  reschedule_appointment,
  general_inquiry,
  update_patient_details,
  transfer_call,
  others,
}

export interface IRetellCall extends Document {
  call_id: string;
  agent_id: string;
  call_type?: string;
  agent_version?: number;
  call_status?: string;
  start_timestamp?: number;
  end_timestamp?: number;
  duration_ms?: number;
  transcript?: string;
  recording_url?: string;
  public_log_url?: string;
  disconnection_reason?: string;
  latency?: {
    user_avg_rtt_ms?: number;
    user_max_rtt_ms?: number;
    user_min_rtt_ms?: number;
  };
  call_analysis?: {
    call_summary?: string;
    in_voicemail?: boolean;
    user_sentiment?: string;
    call_successful?: boolean;
    custom_analysis_data?: Record<string, object>;
  };
  opt_out_sensitive_data_storage?: boolean;
  opt_in_signed_url?: boolean;
  custom?: {
    intent: RetellCallIntent;
  };
}

const RetellCallSchema = new Schema<IRetellCall>(
  {
    call_id: { type: String, required: true, unique: true },
    agent_id: { type: String, required: true },
    call_type: String,
    agent_version: Number,
    call_status: String,
    start_timestamp: Number,
    end_timestamp: Number,
    duration_ms: Number,
    transcript: String,
    recording_url: String,
    public_log_url: String,
    disconnection_reason: String,
    latency: {
      user_avg_rtt_ms: Number,
      user_max_rtt_ms: Number,
      user_min_rtt_ms: Number,
    },
    call_analysis: {
      call_summary: String,
      in_voicemail: Boolean,
      user_sentiment: String,
      call_successful: Boolean,
      custom_analysis_data: Schema.Types.Mixed,
    },
    opt_out_sensitive_data_storage: Boolean,
    opt_in_signed_url: Boolean,
    custom: {
      intent: String,
    },
  },
  { timestamps: { createdAt: 'created_at', updatedAt: 'updated_at' } },
);

export const RetellCall = mongoose.model<IRetellCall>(
  'RetellCall',
  RetellCallSchema,
);

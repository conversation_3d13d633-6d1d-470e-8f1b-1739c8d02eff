import { AxiosError } from 'axios';
import {
  IRetellCall,
  RetellCall,
  RetellCallIntent,
} from '../models/RetellCall';
import logger from '../modules/logger';
import { IAuthUser } from '../types';
import { hasUserAccessToThisClinic } from '../utils';
import HttpApiError from '../utils/http_api_error';
import { getClinicDetails } from './clinic_controller';
import { getResponseFromClaude3 } from '../modules/bedrock-claude3';

export const createOrUpdateRetellCall = async (
  data: IRetellCall,
  event: 'call_started' | 'call_ended' | 'call_analyzed',
): Promise<void> => {
  try {
    const existing = await RetellCall.findOne({ call_id: data.call_id });

    if (data.transcript && event === 'call_analyzed') {
      // identify & store intent for analytics
      try {
        data.custom = {
          intent: (await getIntentFromTranscript(data.transcript)).intent,
        };
      } catch (error) {
        logger.error((error as AxiosError)?.response?.data ?? error);
      }
    }

    if (existing) {
      await RetellCall.updateOne({ call_id: data.call_id }, { $set: data });
      logger.info(`RetellCall updated for call_id: ${data.call_id}`);
    } else {
      await RetellCall.create(data);
      logger.info(`RetellCall created for call_id: ${data.call_id}`);
    }
  } catch (error) {
    logger.error(
      `Failed to create or update RetellCall: ${(error as Error).message}`,
      error,
    );
    throw error;
  }
};

export const listAllCallsForAClinic = async (
  user: IAuthUser,
  params: {
    clinicId: string;
    startDate?: string;
    endDate?: string;
  },
): Promise<{
  summary: {
    total_calls: number;
    average_duration_in_minutes: number;
    total_duration_in_minutes: number;
  };
  calls: IRetellCall[];
}> => {
  logger.debug(params);
  try {
    // for non-admin, check clinic access
    if (!user?.admin) {
      if (!(await hasUserAccessToThisClinic(user, params.clinicId))) {
        throw new HttpApiError(403, 'You do not have access to this clinic');
      }
    }

    const clinicDetails = await getClinicDetails({ clinicId: params.clinicId });

    if (!clinicDetails) {
      throw new HttpApiError(400, 'This clinic does not exist');
    }

    const start = params.startDate
      ? new Date(params.startDate)
      : new Date(new Date().setHours(0, 0, 0, 0)); // today start
    const end = params.endDate
      ? new Date(params.endDate)
      : new Date(new Date().setHours(23, 59, 59, 999)); // today end

    const matchQuery = {
      agent_id: clinicDetails.agent_id,
      created_at: { $gte: start, $lte: end },
    };

    // Step 1: Summary aggregation
    const summaryAgg = await RetellCall.aggregate([
      { $match: matchQuery },
      {
        $group: {
          _id: null,
          total_calls: { $sum: 1 },
          total_duration_ms: { $sum: '$duration_ms' },
        },
      },
    ]);

    const total_calls = summaryAgg[0]?.total_calls || 0;
    const total_duration_ms = summaryAgg[0]?.total_duration_ms || 0;
    const average_duration_in_minutes =
      total_calls > 0
        ? Math.round((total_duration_ms / total_calls / 60000) * 100) / 100
        : 0;

    // Step 2: Get latest 20 calls
    const projection = {
      call_id: 1,
      agent_id: 1,
      call_type: 1,
      call_status: 1,
      start_timestamp: 1,
      end_timestamp: 1,
      duration_ms: 1,
      transcript: 1,
      recording_url: 1,
      public_log_url: 1,
      disconnection_reason: 1,
      'call_analysis.call_successful': 1,
      'call_analysis.user_sentiment': 1,
      created_at: 1,
      'custom.intent': 1,
    };

    const calls = await RetellCall.find(matchQuery, projection)
      .sort({ created_at: -1 })
      .limit(100) // max 100 calls in list
      .lean();

    return {
      summary: {
        total_calls,
        average_duration_in_minutes,
        total_duration_in_minutes: Math.round(total_duration_ms / (1000 * 60)),
      },
      calls,
    };
  } catch (error) {
    logger.error(error);
    throw error;
  }
};

const getIntentFromTranscript = async (
  transcript: string,
): Promise<{
  intent: RetellCallIntent;
}> => {
  logger.info(`getIntentFromTranscript called for transcript`);
  logger.info(transcript);
  const intents = Object.keys(RetellCallIntent); // ['book_appointment', 'cancel_appointment', ...]
  const prompt = `
You are an AI assistant helping classify call transcripts for a healthcare clinic.

Your task is to determine the **intent** of the call from the transcript below.

Possible intents: ${intents.join(', ')}

Respond with below JSON object:
{ "intent": "book_appointment" }

Here is the transcript:
"""
${transcript}
"""
`;
  const openAiResponse = await getResponseFromClaude3(prompt);
  logger.info(openAiResponse);
  return openAiResponse as {
    intent: RetellCallIntent;
  };
};

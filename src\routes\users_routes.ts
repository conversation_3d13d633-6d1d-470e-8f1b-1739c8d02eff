import { AxiosError } from 'axios';
import express, { type Response } from 'express';
import { IApiRequest } from '../types';
import {
  createOrUpdateUser,
  createUserSupportRequest,
  getUserDetails,
} from '../controllers/user';
import HttpApiError from '../utils/http_api_error';
import { getGenericErrorResponse } from '../utils';
import { IUser } from '../models/User';

const router = express.Router();

router.get('/me', (req: IApiRequest, res: Response) => {
  if (!req.user) {
    res.status(401).json({ message: 'Unauthorized access' });
    return;
  }

  getUserDetails(req.user)
    .then((data) => {
      res.json({ data });
    })
    .catch((error: Error) => {
      const statusCode =
        (error as AxiosError).response?.status ??
        (error as HttpApiError).statusCode ??
        500;
      res.status(statusCode);
      const fallbackErrorResponse = getGenericErrorResponse();
      fallbackErrorResponse.error.message = error.message;
      res.json((error as AxiosError).response?.data ?? fallbackErrorResponse);
    });
});

router.post('/', (req: IApiRequest, res: Response) => {
  if (!req.user) {
    res.status(401).json({ message: 'Unauthorized access' });
    return;
  }

  const payload: {
    user_data: Partial<IUser>;
    id_token: string;
  } = req?.body ?? {};
  payload.id_token = req.headers['x-id-token'] as string;

  if (!payload?.id_token || !payload?.user_data) {
    res.status(400).json({
      error: {
        message: 'Mising user_data or x-id-token header',
      },
    });
    return;
  }

  createOrUpdateUser(req.user, payload)
    .then((data) => {
      res.json({ data });
    })
    .catch((error: Error) => {
      const statusCode =
        (error as AxiosError).response?.status ??
        (error as HttpApiError).statusCode ??
        500;
      res.status(statusCode);
      const fallbackErrorResponse = getGenericErrorResponse();
      fallbackErrorResponse.error.message = error.message;
      res.json((error as AxiosError).response?.data ?? fallbackErrorResponse);
    });
});

router.post('/support-request', (req: IApiRequest, res: Response) => {
  if (!req.user) {
    res.status(401).json({ message: 'Unauthorized access' });
    return;
  }

  const payload: {
    subject: string;
    message: string;
  } = req?.body ?? {};

  if (!payload?.subject || !payload?.message) {
    res.status(400).json({
      error: {
        message: 'Mising subject/message',
      },
    });
    return;
  }

  createUserSupportRequest(req.user, payload)
    .then((data) => {
      res.json({ data });
    })
    .catch((error: Error) => {
      const statusCode =
        (error as AxiosError).response?.status ??
        (error as HttpApiError).statusCode ??
        500;
      res.status(statusCode);
      const fallbackErrorResponse = getGenericErrorResponse();
      fallbackErrorResponse.error.message = error.message;
      res.json((error as AxiosError).response?.data ?? fallbackErrorResponse);
    });
});

export default router;

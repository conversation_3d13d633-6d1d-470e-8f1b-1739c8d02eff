import { ObjectId } from 'mongodb';
import logger from '../modules/logger';
import { AdminPrivilegeLevel, IAuthUser } from '../types';
import { Clinic, IClinic } from '../models/Clinic';
import { IUserClinicRole, UserClinicRole } from '../models/UserClinicRole';
import HttpApiError from '../utils/http_api_error';
import {
  checkIfClinicUpdateIsAllowedForAdmin,
  hasUserAccessToThisClinic,
} from '../utils';
import { FilterQuery } from 'mongoose';
import { IUser } from '../models/User';
import { getUserDetails } from './user';

// Voice agent function-calls will have agentId
// Backend API calls will pass businessId
/* export const getClinicDetailsForTesting = async (): Promise<IClinic> => {
  const mockClinic: Partial<IClinic> = {
    agent_id: 'agent_b3c30067427c98658b6523323b',
    // agent_id: 'agent_d5ec7553491a7308b1e725d226', //added by raj
    crm_details: {
      name: CrmSystems.CLINIKO,
      auth_details: {
        api_key:
          // 'MS0xNjY0OTQxMjU4ODA1NjE4NjQxLWNnejJtWlQ3K2JzWVVIYm5iYWZ0YWxtdjRIcTlqOTRY-au4',
          'MS0xNjkwMzAyNzE5MjI4NjUwODM0LThYdnBRZkk3bXd6bTRFTjNWNGNUMXRpL0ZZa2k4REdu-au4 ', // hil auth token
      },
      custom_fields: {
        business_id: '1663438324993369597', // added hil business id for testing
        appointment_type_id: '1663438324280337701',
      },
    },
    clinic_name: 'My GP Gungahlin',
    human_transfer_destination_number: '+917206479844',
    created_at: new Date(),
    updated_at: new Date(),
    smart_reception_phone_no: '',
    clinic_address: '',
    clinic_email: '',
    _id: '', // This will be populated by Mongoose later, you can leave it as an empty string for the mock.
  };

  // Returning the mock object as an IClinic type
  return mockClinic as IClinic;
};/*

/**
 * Get business details by either clinicId or agentId
 */
// todo hide CRM auth details
export const getClinicDetails = async (params: {
  agentId?: string;
  clinicId?: string;
}): Promise<IClinic | null> => {
  try {
    if (!params.clinicId && !params.agentId) {
      throw new Error('Either clinicId or agentId must be provided');
    }

    const query = params.clinicId
      ? { _id: new ObjectId(params.clinicId) } // MongoDB _id query
      : { agent_id: params.agentId };

    const clinic = await Clinic.findOne(query).exec();

    return clinic;
  } catch (error) {
    logger.error('Error in getClinicDetails:', error);
    throw error;
  }
};

export const getUserClinicDetails = async (
  authUser: IAuthUser,
  payload: {
    clinicId: string;
  },
): Promise<(IClinic & { user_clinic_role?: 'master' | 'staff' }) | null> => {
  const { clinicId } = payload;

  if (!authUser?.admin) {
    const hasAccess = await hasUserAccessToThisClinic(authUser, clinicId);
    if (!hasAccess) {
      throw new HttpApiError(403, 'You do not have access to this clinic');
    }
  }

  const clinicDetails = await getClinicDetails({ clinicId });
  if (!clinicDetails) return null;

  const userClinicRoleDoc = await UserClinicRole.findOne({
    user_id: authUser.uid,
    clinic_id: clinicId,
  }).select('role');

  return {
    ...clinicDetails.toObject(),
    user_clinic_role: userClinicRoleDoc?.role,
  };
};

// Mongoose schema required validations aren't being enforced during findOneAndUpdate() operations by default
export const createOrUpdateClinic = async (
  authUser: IAuthUser,
  clinicData: Partial<IClinic>,
): Promise<IClinic> => {
  const clinicId = clinicData._id || new ObjectId();
  const now = new Date();
  const isNewClinic = !clinicData._id;

  try {
    let clinic: IClinic | null;

    if (isNewClinic) {
      let existingClinic: IClinic | null = null;
      const orConditions: FilterQuery<IClinic>[] = [];

      if (clinicData.clinic_email) {
        orConditions.push({ clinic_email: clinicData.clinic_email });
      }
      if (clinicData.clinic_phone) {
        orConditions.push({ clinic_phone: clinicData.clinic_phone });
      }
      if (clinicData.clinic_website) {
        orConditions.push({ clinic_website: clinicData.clinic_website });
      }

      if (orConditions.length > 0) {
        existingClinic = await Clinic.findOne({ $or: orConditions });
      }

      if (existingClinic) {
        throw new Error(
          'Clinic already exists with this email, phone, or website',
        );
      }

      // CREATE - Use create() which respects schema validation
      clinic = await Clinic.create({
        ...clinicData,
        _id: clinicId,
        created_at: now,
        updated_at: now,
      });

      await UserClinicRole.create({
        user_id: authUser.uid,
        clinic_id: clinicId,
        role: 'master',
      });
      logger.info(
        `Clinic ${clinicId} created and assigned to user ${authUser.uid}`,
      );
    } else {
      const userClinicRole = await getUserClinicRole(authUser, clinicData._id!);
      if (userClinicRole?.role !== 'master') {
        throw new HttpApiError(
          403,
          'You do not have write access to this clinic ',
        );
      }
      // UPDATE - Use findOneAndUpdate with validation
      clinic = await Clinic.findOneAndUpdate(
        { _id: clinicId },
        {
          $set: {
            ...clinicData,
            updated_at: now,
          },
        },
        {
          new: true,
          runValidators: true, // Enforce schema validation
        },
      ).exec();

      if (!clinic) {
        throw new HttpApiError(400, `Clinic with ID ${clinicId} not found`);
      }
      logger.info(`Clinic ${clinicId} updated successfully`);
    }

    return clinic;
  } catch (error) {
    logger.error('Error in createOrUpdateClinic:', error);
    throw error;
  }
};

/**
 * Get paginated list of clinics
 */
// todo hide CRM auth details
export const getUserClinics = async (
  authUser: IAuthUser,
  options: {
    limit?: number;
    skip?: number;
  },
): Promise<
  Array<
    IClinic & {
      user_clinic_role: 'master' | 'staff';
    }
  >
> => {
  try {
    // Step 1: Get all clinic IDs assigned to the user
    const userClinicRoles = await UserClinicRole.find({
      user_id: authUser.uid,
    }).select(['clinic_id', 'role']);

    const clinicIds = userClinicRoles.map((link) => link.clinic_id);

    if (clinicIds.length === 0) {
      return []; // No assigned clinics
    }

    // Step 2: Fetch clinic details
    const clinics = await Clinic.find({
      _id: { $in: clinicIds },
      is_active: true,
    })
      .lean()
      .skip(options.skip || 0)
      .limit(options.limit || 50)
      .exec();
    return clinics.map((clinic) => {
      return {
        ...clinic,
        user_clinic_role:
          userClinicRoles?.find(
            (item) => item.clinic_id?.toString() === clinic._id?.toString(),
          )?.role ?? 'staff',
      } as unknown as IClinic & { user_clinic_role: 'master' | 'staff' };
    });
  } catch (error) {
    logger.error('Error in getClinics:', error);
    throw error;
  }
};

export const getAllClinics = async (
  user: IAuthUser,
  options: {
    limit?: number;
    skip?: number;
  },
): Promise<IClinic[]> => {
  try {
    if (user?.role !== 'admin') {
      throw new Error('Only admin can get all the clinics');
    }
    // Use Mongoose's find method with pagination options
    const clinics = await Clinic.find({ is_active: true })
      .skip(options.skip || 0)
      .limit(options.limit || 50)
      .exec();

    return clinics;
  } catch (error) {
    logger.error('Error in getAllClinics:', error);
    throw error;
  }
};

export const createOrUpdateClinicForAdmin = async (
  user: IAuthUser,
  clinicData: Partial<IClinic>,
): Promise<IClinic> => {
  if (user?.role !== 'admin') {
    throw new HttpApiError(403, 'Only admin can create or update clinics');
  }
  if (
    !user?.admin?.privilege_level ||
    ![AdminPrivilegeLevel.OWNER, AdminPrivilegeLevel.EDITOR].includes(
      user?.admin?.privilege_level,
    )
  ) {
    throw new HttpApiError(403, 'You do not have privilege for this operation');
  }

  const clinicId = clinicData._id || new ObjectId();
  const now = new Date();
  const isNewClinic = !clinicData._id;

  try {
    // Authorization check

    let clinic: IClinic | null;

    if (isNewClinic) {
      let existingClinic: IClinic | null = null;
      const orConditions: FilterQuery<IClinic>[] = [];

      if (clinicData.clinic_email) {
        orConditions.push({ clinic_email: clinicData.clinic_email });
      }
      if (clinicData.clinic_phone) {
        orConditions.push({ clinic_phone: clinicData.clinic_phone });
      }
      if (clinicData.clinic_website) {
        orConditions.push({ clinic_website: clinicData.clinic_website });
      }

      if (orConditions.length > 0) {
        existingClinic = await Clinic.findOne({ $or: orConditions });
      }

      if (existingClinic) {
        throw new Error(
          'Clinic already exists with this email, phone, or website',
        );
      }
      // CREATE - Use create() which respects schema validation
      clinic = await Clinic.create({
        ...clinicData,
        _id: clinicId,
        created_at: now,
        updated_at: now,
      });
      logger.info(
        `Clinic ${clinicId} created successfully by admin ${user.uid}`,
      );
    } else {
      // UPDATE - Use findOneAndUpdate with validation
      //todo-check clinic is active or not
      clinic = await Clinic.findOneAndUpdate(
        { _id: clinicId },
        {
          $set: {
            ...clinicData,
            updated_at: now,
          },
        },
        {
          new: true,
          runValidators: true, // Enforce schema validation
        },
      ).exec();

      if (!clinic) {
        throw new Error(`Clinic with ID ${clinicId} not found`);
      }
      logger.info(
        `Clinic ${clinicId} updated successfully by admin ${user.uid}`,
      );
    }

    return clinic;
  } catch (error) {
    logger.error('Error in createOrUpdateClinicForAdmin:', error);
    throw error;
  }
};

export const getUserClinicRole = async (
  user: IAuthUser,
  clinicId: string,
): Promise<IUserClinicRole | null> => {
  const mapping = await UserClinicRole.findOne({
    user_id: user.uid,
    clinic_id: clinicId,
  });

  return mapping;
};

export const getClinicOwners = async (
  user: IAuthUser,
  clinicId: string,
): Promise<
  Array<{
    user_details: Partial<IUser>;
    mapping: IUserClinicRole;
  }>
> => {
  // for non-admin, check access permission
  if (!user?.admin) {
    if (!(await hasUserAccessToThisClinic(user, clinicId))) {
      throw new HttpApiError(403, 'You do not have access to this clinic ');
    }
  }
  const mappings = await UserClinicRole.find({ clinic_id: clinicId });

  const data = await Promise.all(
    mappings.map(async (mapping) => {
      const userDetails = await getUserDetails({
        uid: mapping.user_id,
        role: 'user',
      });
      return {
        user_details: userDetails,
        mapping,
      };
    }),
  );

  return data;
};

export const updateUserClinicRole = async (
  user: IAuthUser,
  payload: IUserClinicRole,
): Promise<IUserClinicRole | null> => {
  if (!['master', 'staff'].includes(payload.role)) {
    throw new HttpApiError(400, 'Invalid role value');
  }

  if (user?.admin) {
    await checkIfClinicUpdateIsAllowedForAdmin(user);
  } else {
    // user must have master access to this clinic
    const mapping = await UserClinicRole.findOne({
      user_id: user.uid,
      clinic_id: payload.clinic_id,
    });
    if (mapping?.role !== 'master') {
      throw new HttpApiError(
        403,
        'You do not have authority to perform this operation',
      );
    }
  }

  try {
    const updatedMapping = await UserClinicRole.findOneAndUpdate(
      { user_id: payload.user_id, clinic_id: payload.clinic_id },
      { $set: { role: payload.role } },
      { new: true },
    );

    if (!updatedMapping) {
      throw new HttpApiError(400, 'User-clinic mapping not found');
    }

    return updatedMapping;
  } catch (error) {
    logger.error(error);
    throw error;
  }
};

export const deleteUserClinicRole = async (
  user: IAuthUser,
  payload: Partial<IUserClinicRole>,
): Promise<void> => {
  // Permission checks
  if (user?.admin) {
    await checkIfClinicUpdateIsAllowedForAdmin(user);
  } else {
    // Only masters of a clinic can delete mappings
    const mapping = await UserClinicRole.findOne({
      user_id: user.uid,
      clinic_id: payload.clinic_id,
    });

    if (mapping?.role !== 'master') {
      throw new HttpApiError(
        403,
        'You do not have authority to perform this operation',
      );
    }
  }

  try {
    const result = await UserClinicRole.deleteOne({
      user_id: payload.user_id,
      clinic_id: payload.clinic_id,
    });

    if (result.deletedCount === 0) {
      throw new HttpApiError(400, 'User-clinic mapping not found');
    }
  } catch (error) {
    logger.error('Error deleting user clinic role', { error, payload });
    throw error;
  }
};

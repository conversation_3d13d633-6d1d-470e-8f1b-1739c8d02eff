// Auth: https://docs.api.cliniko.com/

import axios, { AxiosError } from 'axios';
import logger from '../../logger';
import {
  IClinikoAvailableTimesParams,
  IClinikoCancelIndividualAppointmentPayload,
  IClinikoCreateIndividualAppointmentPayload,
  IClinikoCreatePatientPayload,
  IClinikoUpdateIndividualAppointmentPayload,
  IClinikoUpdatePatientPayload,
} from './types';

const getHeaders = (): Record<string, string> => {
  return {
    'Content-Type': 'application/json',
    Accept: 'application/json',
  };
};
// https://docs.api.cliniko.com/openapi/individual-appointment/createindividualappointment-post
const createIndividualAppointment = async (params: {
  payload: IClinikoCreateIndividualAppointmentPayload;
  headers: Record<string, string>;
}): Promise<object> => {
  const { payload, headers } = params;
  const apiUrl = 'https://api.au4.cliniko.com/v1/individual_appointments';

  try {
    logger.info('Creating appointment with payload:', payload);

    const response = await axios({
      method: 'post',
      url: apiUrl,
      headers: {
        ...getHeaders(),
        Authorization: headers.Authorization,
      },
      data: payload,
    });

    logger.debug('Appointment created successfully:', response.data);
    return response.data;
  } catch (error) {
    logger.error((error as AxiosError)?.response?.data ?? error);
    throw error;
  }
};

const createPatient = async (params: {
  payload: IClinikoCreatePatientPayload;
  headers: Record<string, string>;
}): Promise<{
  id: string;
}> => {
  const { payload, headers } = params;
  const apiUrl = 'https://api.au4.cliniko.com/v1/patients';

  try {
    logger.info('Creating patient with payload:', payload);

    const response = await axios({
      method: 'post',
      url: apiUrl,
      headers: {
        ...getHeaders(),
        Authorization: headers.Authorization,
      },
      data: payload,
    });

    logger.debug('Patient created successfully:', response.data);
    return response.data;
  } catch (error) {
    logger.error((error as AxiosError)?.response?.data ?? error);
    throw error;
  }
};

const updateIndividualAppointment = async (params: {
  appointment_id: string;
  payload: IClinikoUpdateIndividualAppointmentPayload;
  headers: Record<string, string>;
}): Promise<object> => {
  const { appointment_id, payload, headers } = params;
  const apiUrl = `https://api.au4.cliniko.com/v1/individual_appointments/${appointment_id}`;
  try {
    logger.info(
      `Updating appointment with appointment id : ${appointment_id} and payload: ${payload}`,
    );

    const response = await axios({
      method: 'patch',
      url: apiUrl,
      headers: {
        ...getHeaders(),
        Authorization: headers.Authorization,
      },
      data: payload,
    });

    logger.debug('Appointment updated successfully:', response.data);
    return response.data;
  } catch (error) {
    logger.error((error as AxiosError)?.response?.data ?? error);
    throw error;
  }
};

const cancelIndividualAppointment = async (params: {
  appointment_id: string;
  payload: IClinikoCancelIndividualAppointmentPayload;
  headers: Record<string, string>;
}): Promise<object> => {
  const { appointment_id, payload, headers } = params;
  const apiUrl = `https://api.au4.cliniko.com/v1/individual_appointments/${appointment_id}/cancel`;
  try {
    logger.info(
      `cancelling appointment with appointmentId: ${appointment_id} and payload: ${payload}`,
    );

    const response = await axios({
      method: 'patch',
      url: apiUrl,
      headers: {
        ...getHeaders(),
        Authorization: headers.Authorization,
      },
      data: payload,
    });

    logger.debug('Appointment cancelled successfully:', response.data);
    return response.data;
  } catch (error) {
    logger.error((error as AxiosError)?.response?.data ?? error);
    throw error;
  }
};

const getPatient = async (params: {
  filtersForQuery: string;
  headers: Record<string, string>;
}): Promise<object> => {
  const { filtersForQuery, headers } = params;
  const apiUrl = `https://api.au4.cliniko.com/v1/patients?${filtersForQuery}`;
  try {
    logger.info(`Getting patients with filters: ${filtersForQuery}`);
    const response = await axios.get(apiUrl, {
      headers: {
        ...getHeaders(),
        Authorization: headers.Authorization,
      },
    });
    return response.data;
  } catch (error) {
    logger.error((error as AxiosError)?.response?.data ?? error);
    throw error;
  }
};

const getAppointments = async (params: {
  filtersForQuery: string;
  headers: Record<string, string>;
}): Promise<object> => {
  const { filtersForQuery, headers } = params;
  const apiUrl = `https://api.au4.cliniko.com/v1/individual_appointments?${filtersForQuery}`;
  try {
    logger.info(
      `Getting appointments for a particular patient with filters: ${filtersForQuery}`,
    );
    const response = await axios.get(apiUrl, {
      headers: {
        ...getHeaders(),
        Authorization: headers.Authorization,
      },
    });
    return response.data;
  } catch (error) {
    logger.error((error as AxiosError)?.response?.data ?? error);
    throw error;
  }
};

const updatePatient = async (params: {
  patient_id: string;
  payload: Partial<IClinikoUpdatePatientPayload>;
  headers: Record<string, string>;
}): Promise<object> => {
  const { patient_id, payload, headers } = params;
  const apiUrl = `https://api.au4.cliniko.com/v1/patients/${patient_id}`;
  try {
    logger.info(
      `Updating a patient with patient id: ${patient_id} and payload:,`,
      payload,
    );
    const response = await axios({
      method: 'patch',
      url: apiUrl,
      headers: {
        Authorization: headers.Authorization,
        'Content-Type': 'application/json',
        Accept: 'application/json',
      },
      data: payload,
    });

    logger.debug('Patient updated successfully:', response.data);
    return response.data;
  } catch (error) {
    logger.error((error as AxiosError)?.response?.data ?? error);
    throw error;
  }
};

const getPractitioners = async (params: {
  headers: Record<string, string>;
}): Promise<object> => {
  const { headers } = params;
  const apiUrl = 'https://api.au4.cliniko.com/v1/practitioners';
  try {
    logger.info(`Getting all the practitioners`);
    const response = await axios.get(apiUrl, {
      headers: {
        ...getHeaders(),
        Authorization: headers.Authorization,
      },
    });
    return response.data;
  } catch (error) {
    logger.error((error as AxiosError)?.response?.data ?? error);
    throw error;
  }
};

const getAllAvailableTimes = async (params: {
  availableTimeParams: IClinikoAvailableTimesParams;
  headers: Record<string, string>;
}): Promise<{
  available_times: Array<{
    appointment_start: string;
    appointment_start_aest?: string;
  }>;
}> => {
  const { availableTimeParams, headers } = params;
  const { business_id, practitioner_id, appointment_type_id, from, to } =
    availableTimeParams;
  const apiUrl = `https://api.au4.cliniko.com/v1/businesses/${business_id}/practitioners/${practitioner_id}/appointment_types/${appointment_type_id}/available_times?from=${from}&to=${to}`;

  try {
    logger.info(
      `getting all the available times for business: ${business_id}, practitioner: ${practitioner_id}, appointment_type: ${appointment_type_id}, from: ${from} and to: ${to}`,
    );
    const response = await axios.get(apiUrl, {
      headers: {
        ...getHeaders(),
        Authorization: headers.Authorization,
      },
    });
    return response.data;
  } catch (error) {
    logger.error((error as AxiosError)?.response?.data ?? error);
    throw error;
  }
};

const getPatientActiveCases = async (params: {
  filtersForQuery: string;
  headers: Record<string, string>;
}): Promise<object> => {
  const { headers } = params;
  const apiUrl = `https://api.au4.cliniko.com/v1/patient_cases/active?${params.filtersForQuery}`;
  try {
    logger.info(
      `Getting active patient cases with filters: ${params.filtersForQuery}`,
    );
    const response = await axios.get(apiUrl, {
      headers: {
        ...getHeaders(),
        Authorization: headers.Authorization,
      },
    });
    return response.data;
  } catch (error) {
    logger.error((error as AxiosError)?.response?.data ?? error);
    throw error;
  }
};

const cliniko = {
  createIndividualAppointment,
  createPatient,
  updateIndividualAppointment,
  cancelIndividualAppointment,
  getPatient,
  getAppointments,
  updatePatient,
  getPractitioners,
  getAllAvailableTimes,
  getPatientActiveCases,
};

export default cliniko;

import { IUser, User } from '../../models/User';
import auth from '../../modules/auth';
import logger from '../../modules/logger';
import { IAuthUser } from '../../types';
import HttpApiError from '../../utils/http_api_error';
import { sendUserSupportRequestEmail } from '../email_controller';

export const getUserDetails = async (
  authUser: IAuthUser,
): Promise<Partial<IUser>> => {
  const { uid } = authUser;

  const userDetails = await User.findOne({
    uid,
  });
  return (
    userDetails ?? {
      uid,
    }
  );
};

export const createOrUpdateUser = async (
  authUser: IAuthUser,
  payload: {
    user_data: Partial<IUser>;
    id_token: string;
  },
): Promise<IUser> => {
  const { user_data: userDataInput, id_token: idToken } = payload;
  const now = new Date();

  const decoded = await auth.verifyCognitoIdToken(idToken);

  if (!decoded?.email_verified) {
    throw new HttpApiError(403, 'Email is unverified');
  }

  const userData: Partial<IUser> = {
    ...userDataInput,
    uid: authUser.uid,
    email: decoded.email as string,
  };

  try {
    const existingUser = await User.findOne({ uid: authUser.uid });

    if (existingUser) {
      const updatedUser = await User.findOneAndUpdate(
        { uid: existingUser.uid },
        {
          $set: {
            ...userData,
            updated_at: now,
          },
        },
        {
          new: true,
          runValidators: true,
        },
      ).exec();

      if (!updatedUser) {
        throw new Error(`Failed to update user with UID ${existingUser.uid}`);
      }

      return updatedUser;
    } else {
      const newUser = await User.create({
        ...userData,
        created_at: now,
        updated_at: now,
      });

      return newUser;
    }
  } catch (error) {
    logger.error('Error in createOrUpdateUser:', error);
    throw error;
  }
};

export const createUserSupportRequest = async (
  authUser: IAuthUser,
  payload: {
    message: string;
    subject: string;
  },
): Promise<object> => {
  const userDetails = await getUserDetails(authUser);
  const response = await sendUserSupportRequestEmail({
    ...payload,
    email: userDetails?.email ?? '',
    first_name: userDetails?.first_name ?? '',
  });
  return response;
};

import {
  PhoneNumberImportParams,
  PhoneNumberResponse,
  PhoneNumberUpdateParams,
} from 'retell-sdk/resources/phone-number';
import {
  createOrUpdateClinicForAdmin,
  getClinicDetails,
} from '../clinic_controller';
import {
  getPhoneNumberDetails,
  importPhoneNumber,
  updatePhoneNumber,
} from '../../modules/retell';
import logger from '../../modules/logger';
import { SIP_TRUNK_TERMINATION_URI } from '../../constants';
import {
  ImportPhoneNumberPayload,
  UpdatePhoneNumberPayload,
} from '../../types/admin';
import { AdminPrivilegeLevel, IAuthUser } from '../../types';
import { listIncomingPhoneNumber } from '../../modules/twilio';
import HttpApiError from '../../utils/http_api_error';
import { IUser, User } from '../../models/User';
import {
  addUserToGroup,
  findUserByEmail,
  getUserFromCognito,
  getUsersInGroup,
  removeUserFromGroup,
  updateUserAttributes,
} from '../../modules/cognito';
import { AxiosError } from 'axios';

const IMPORT_PH_CRED_USERNAME = process.env.IMPORT_PH_CRED_USERNAME ?? '';
const IMPORT_PH_CRED_PASSWORD = process.env.IMPORT_PH_CRED_PASSWORD ?? '';

export const assignTwilioPhoneNumberToVoiceAgent = async (
  user: IAuthUser,
  params: ImportPhoneNumberPayload,
): Promise<PhoneNumberResponse> => {
  try {
    const { phone_number, clinic_id } = params;
    //todo-check whether clinic is active or not
    const clinicDetails = await getClinicDetails({
      clinicId: clinic_id ?? '',
    });
    if (!clinicDetails) {
      throw new HttpApiError(400, 'This clinic does not exist');
    }
    const agent_id = clinicDetails?.agent_id;
    const importPhoneNumberParams: PhoneNumberImportParams = {
      phone_number,
      termination_uri: SIP_TRUNK_TERMINATION_URI,
      sip_trunk_auth_username: IMPORT_PH_CRED_USERNAME,
      sip_trunk_auth_password: IMPORT_PH_CRED_PASSWORD,
      inbound_agent_id: agent_id,
      outbound_agent_id: agent_id,
      nickname: phone_number,
    };

    const phoneNumberResponse: PhoneNumberResponse = await importPhoneNumber(
      importPhoneNumberParams,
    );
    return phoneNumberResponse;
  } catch (error) {
    logger.error(error);
    throw error;
  }
};

export const listTwilioPhoneNumbers = async (): Promise<object> => {
  try {
    const response = await listIncomingPhoneNumber();
    return response;
  } catch (error) {
    logger.error((error as AxiosError)?.response?.data ?? error);
    throw error;
  }
};

export const registerAdmin = async (
  user: IAuthUser,
  payload: {
    email: string;
    privilege_level: AdminPrivilegeLevel;
  },
): Promise<object> => {
  // only owner can add admin
  if (user?.admin?.privilege_level !== AdminPrivilegeLevel.OWNER) {
    throw new HttpApiError(403, 'You do not have privilege to add new admin.');
  }
  try {
    const { email, privilege_level: privilegeLevel } = payload;
    if (!email || !privilegeLevel) {
      throw new HttpApiError(400, 'Missing email or privilege_level');
    }
    if (
      ![
        AdminPrivilegeLevel.OWNER,
        AdminPrivilegeLevel.EDITOR,
        AdminPrivilegeLevel.VIEWER,
      ].includes(privilegeLevel)
    ) {
      throw new HttpApiError(
        400,
        `Valid values of privilege_level are: ${Object.values(AdminPrivilegeLevel).join(', ')}`,
      );
    }
    // get uid
    const userDetails = await User.findOne({
      email: payload.email,
    });
    if (!userDetails?.uid) {
      throw new HttpApiError(400, 'Bad email, details not found');
    }
    const uid = userDetails.uid;

    // 1. Add to 'admin' group
    await addUserToGroup(uid, 'admin');

    // 2. Set custom:admin_level attribute
    await updateUserAttributes(uid, {
      'custom:admin_level': privilegeLevel,
    });

    // 3. Fetch updated user from Cognito
    const updatedUser = await getUserFromCognito(uid);
    return updatedUser;
  } catch (error) {
    logger.error(error);
    throw error;
  }
};

export const removeAdmin = async (
  user: IAuthUser,
  payload: {
    uid: string;
  },
): Promise<object> => {
  // Only owner can remove an admin
  if (user?.admin?.privilege_level !== AdminPrivilegeLevel.OWNER) {
    throw new HttpApiError(
      403,
      'You do not have privilege to remove an admin.',
    );
  }

  try {
    const { uid } = payload;
    if (!uid) {
      throw new HttpApiError(400, 'Missing uid');
    }

    // 1. Remove user from 'admin' group
    await removeUserFromGroup(uid, 'admin');

    // 2. Remove custom:admin_level attribute
    await updateUserAttributes(uid, {
      'custom:admin_level': '',
    });

    // 3. Fetch updated user from Cognito
    const updatedUser = await getUserFromCognito(uid);
    return updatedUser;
  } catch (error) {
    logger.error(error);
    throw error;
  }
};

export const getAllUsers = async (
  user: IAuthUser,
  options: {
    limit?: number;
    skip?: number;
  },
): Promise<IUser[]> => {
  try {
    if (user?.role !== 'admin') {
      throw new Error('Only admin can get all the clinics');
    }
    // Use Mongoose's find method with pagination options
    const users = await User.find()
      .skip(options.skip || 0)
      .limit(options.limit || 50)
      .exec();

    return users;
  } catch (error) {
    logger.error(error);
    throw error;
  }
};

export const getAllAdmins = async (
  user: IAuthUser,
): Promise<(IUser & { admin_level?: AdminPrivilegeLevel })[]> => {
  try {
    if (user?.role !== 'admin') {
      throw new Error('Only admin can get all the admins');
    }

    // Step 1: Get all admin users from Cognito with their privilege levels
    const cognitoAdmins = await getUsersInGroup('admin');
    logger.info(cognitoAdmins);
    if (cognitoAdmins.length === 0) {
      return [];
    }

    const adminUids = cognitoAdmins.map((admin) => admin.uid);

    // Step 2: Fetch all admin users from MongoDB
    const adminsFromDb = await User.find({ uid: { $in: adminUids } }).exec();

    // Step 3: Create a map from uid -> privilege level for quick lookup
    const privilegeMap = new Map<string, AdminPrivilegeLevel | undefined>();
    cognitoAdmins.forEach(({ uid, admin_level }) => {
      privilegeMap.set(uid, admin_level);
    });

    // Step 4: Merge Mongo user data with privilege level from Cognito
    const adminsWithPrivilege = adminsFromDb.map((admin) => ({
      ...admin.toObject(),
      admin_level: privilegeMap.get(admin.uid),
    })) as unknown as (IUser & {
      admin_level?: AdminPrivilegeLevel;
    })[];

    return adminsWithPrivilege;
  } catch (error) {
    logger.error(error);
    throw error;
  }
};

// do not expose via routes
export const checkAndCreateFirstAdmin = async () => {
  // todo: change after handover
  const firstAdminEmail = '<EMAIL>';
  const adminGroupName = 'admin';
  const ownerPrivilege = 'owner' as AdminPrivilegeLevel;

  try {
    const user = await findUserByEmail(firstAdminEmail);
    if (!user) {
      logger.error(`User with email ${firstAdminEmail} not found in Cognito`);
      return;
    }
    const username = user.Username!;

    // 2. Add user to admin group
    await addUserToGroup(username, adminGroupName);

    // 3. Update admin privilege level attribute
    await updateUserAttributes(username, {
      'custom:admin_level': ownerPrivilege,
    });

    logger.info('checkAndCreateFirstAdmin success', user);
  } catch (error) {
    logger.error((error as Error)?.message ?? error);
  }
};

export const updateTwilioPhoneNumber = async (
  user: IAuthUser,
  params: UpdatePhoneNumberPayload,
): Promise<PhoneNumberResponse | null> => {
  try {
    if (
      !user?.admin?.privilege_level ||
      ![AdminPrivilegeLevel.OWNER, AdminPrivilegeLevel.EDITOR].includes(
        user?.admin?.privilege_level,
      )
    ) {
      throw new HttpApiError(
        403,
        'You do not have privilege for this operation',
      );
    }

    const { phone_number, clinic_id } = params;

    //todo-check whether clinic is active or not
    const clinicDetails = await getClinicDetails({
      clinicId: clinic_id ?? '',
    });
    if (!clinicDetails?.agent_id) {
      throw new HttpApiError(
        400,
        'This clinic does not exist or agent is not yet created',
      );
    }
    const agent_id = clinicDetails?.agent_id ?? '';

    let updatedPhoneNumberResponse: PhoneNumberResponse | null = null;

    // first check if this phone number is already assigned
    try {
      const phoneNumberDetails = await getPhoneNumberDetails(phone_number);

      // exising phone number flow
      const updatePhoneNumberParams: PhoneNumberUpdateParams = {
        inbound_agent_id: agent_id,
        outbound_agent_id: agent_id,
      };
      updatedPhoneNumberResponse = await updatePhoneNumber(
        phone_number,
        updatePhoneNumberParams,
      );

      // update previous clinic
      const previousClinicDetails = await getClinicDetails({
        agentId: phoneNumberDetails.inbound_agent_id ?? '',
      });
      if (previousClinicDetails) {
        await createOrUpdateClinicForAdmin(user, {
          _id: previousClinicDetails._id,
          smart_reception_phone_no: '',
        });
      }
    } catch (error) {
      logger.error(error);
      // this is a new phone number, import and then assign
      // todo check error code
      updatedPhoneNumberResponse = await assignTwilioPhoneNumberToVoiceAgent(
        user,
        params,
      );
    }
    if (updatedPhoneNumberResponse) {
      // update smart_reception_phone_no in clinics (remove from old, add to new)
      await createOrUpdateClinicForAdmin(user, {
        _id: clinic_id,
        smart_reception_phone_no: phone_number,
      });
    }
    return updatedPhoneNumberResponse;
  } catch (error) {
    logger.error(error);
    throw error;
  }
};

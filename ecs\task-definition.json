{"taskDefinitionArn": "arn:aws:ecs:ap-southeast-2:************:task-definition/smart-reception-be:1", "containerDefinitions": [{"name": "smart-reception-be", "image": "************.dkr.ecr.ap-southeast-2.amazonaws.com/smart-reception-be:latest", "cpu": 0, "portMappings": [{"name": "smart-reception-be-4000-tcp", "containerPort": 4000, "hostPort": 4000, "protocol": "tcp", "appProtocol": "http"}], "essential": true, "environment": [{"name": "PORT", "value": "4000"}, {"name": "NODE_ENV", "value": "production"}, {"name": "COGNITO_USER_POOL_ID", "value": "ap-southeast-2_lYVonMyEL"}, {"name": "COGNITO_USER_POOL_CLIENT_ID", "value": "50dsf5l0ase8fr380gq4drpksp"}], "environmentFiles": [], "mountPoints": [], "volumesFrom": [], "ulimits": [], "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-group": "/ecs/smart-reception-be", "mode": "non-blocking", "awslogs-create-group": "true", "max-buffer-size": "25m", "awslogs-region": "ap-southeast-2", "awslogs-stream-prefix": "ecs"}, "secretOptions": []}, "secrets": [{"name": "RETELL_API_KEY", "valueFrom": "arn:aws:secretsmanager:ap-southeast-2:************:secret:prod/smart-reception-be-YqNw0e:RETELL_API_KEY::"}, {"name": "TWILIO_ACCOUNT_SID", "valueFrom": "arn:aws:secretsmanager:ap-southeast-2:************:secret:prod/smart-reception-be-YqNw0e:TWILIO_ACCOUNT_SID::"}, {"name": "TWILIO_AUTH_TOKEN", "valueFrom": "arn:aws:secretsmanager:ap-southeast-2:************:secret:prod/smart-reception-be-YqNw0e:TWILIO_AUTH_TOKEN::"}, {"name": "IMPORT_PH_CRED_USERNAME", "valueFrom": "arn:aws:secretsmanager:ap-southeast-2:************:secret:prod/smart-reception-be-YqNw0e:IMPORT_PH_CRED_USERNAME::"}, {"name": "IMPORT_PH_CRED_PASSWORD", "valueFrom": "arn:aws:secretsmanager:ap-southeast-2:************:secret:prod/smart-reception-be-YqNw0e:IMPORT_PH_CRED_PASSWORD::"}, {"name": "AMAZON_DOC_DB_USERNAME", "valueFrom": "arn:aws:secretsmanager:ap-southeast-2:************:secret:prod/smart-reception-be-YqNw0e:AMAZON_DOC_DB_USERNAME::"}, {"name": "AMAZON_DOC_DB_PASSWORD", "valueFrom": "arn:aws:secretsmanager:ap-southeast-2:************:secret:prod/smart-reception-be-YqNw0e:AMAZON_DOC_DB_PASSWORD::"}, {"name": "OPEN_AI_API_KEY", "valueFrom": "arn:aws:secretsmanager:ap-southeast-2:************:secret:prod/smart-reception-be-YqNw0e:OPEN_AI_API_KEY::"}], "systemControls": []}], "family": "smart-reception-be", "taskRoleArn": "arn:aws:iam::************:role/smart-reception-ecs-task-role", "executionRoleArn": "arn:aws:iam::************:role/ecsTaskExecutionRole", "networkMode": "awsvpc", "revision": 1, "volumes": [], "status": "ACTIVE", "requiresAttributes": [{"name": "com.amazonaws.ecs.capability.logging-driver.awslogs"}, {"name": "ecs.capability.execution-role-awslogs"}, {"name": "com.amazonaws.ecs.capability.ecr-auth"}, {"name": "com.amazonaws.ecs.capability.docker-remote-api.1.19"}, {"name": "ecs.capability.secrets.asm.environment-variables"}, {"name": "com.amazonaws.ecs.capability.docker-remote-api.1.28"}, {"name": "com.amazonaws.ecs.capability.task-iam-role"}, {"name": "ecs.capability.execution-role-ecr-pull"}, {"name": "com.amazonaws.ecs.capability.docker-remote-api.1.18"}, {"name": "ecs.capability.task-eni"}, {"name": "com.amazonaws.ecs.capability.docker-remote-api.1.29"}], "placementConstraints": [], "compatibilities": ["EC2", "FARGATE"], "requiresCompatibilities": ["FARGATE"], "cpu": "512", "memory": "1024", "runtimePlatform": {"cpuArchitecture": "X86_64", "operatingSystemFamily": "LINUX"}, "registeredAt": "2025-04-17T13:05:54.280Z", "registeredBy": "arn:aws:iam::************:user/varun", "tags": []}
import mongoose, { Schema, Document } from 'mongoose';

export interface IClinic extends Document {
  clinic_name: string;
  clinic_addresses?: Array<{
    address: string;
    full_address: string;
    business_location_id: string; // Business ID in CLINIKO
    inboundEnabled? : boolean;
    reminderHoursBefore?: number;
  }>;
  clinic_email: string;
  clinic_phone?: string;
  clinic_website?: string;
  agent_id?: string;
  crm_details: {
    name: string;
    auth_details: {
      api_key: string;
      api_endpoint?: string;
    };
    custom_fields: {
      appointment_type_id: string;
    };
  };
  // E.164 Format
  human_transfer_destination_number?: string;
  smart_reception_phone_no?: string;
  created_at: Date;
  updated_at: Date;
  is_active: boolean;
  _id: string;
  diagnostic_services?: Array<{
    name: string;
    is_referral_required: boolean;
  }>;
  inboundEnabled?: boolean;
  reminderHoursBefore?: number;
}

const ClinicSchema = new Schema<IClinic>(
  {
    clinic_name: { type: String, required: true },
    clinic_addresses: {
      type: Array<{
        address: { type: string };
        full_address: { type: string };
        business_location_id: { type: string };
      }>,
      required: false,
    },
    clinic_email: { type: String, required: true, unique: true },
    clinic_phone: { type: String, required: false, unique: true, sparse: true },
    clinic_website: {
      type: String,
      required: false,
      unique: true,
      sparse: true,
    },
    agent_id: { type: String, required: false },
    crm_details: {
      name: { type: String },
      auth_details: {
        api_key: { type: String },
        api_endpoint: { type: String },
      },
      custom_fields: {
        appointment_type_id: { type: String },
      },
    },
    human_transfer_destination_number: { type: String },
    smart_reception_phone_no: { type: String },
    is_active: { type: Boolean, default: true },
    inboundEnabled: { type: Boolean, default: false },
    reminderHoursBefore: { type: Number, default: 24 },
    diagnostic_services: Array<{
      name: { type: string; required: true };
      is_referral_required: { type: boolean; required: true };
    }>,
    
  },
  { timestamps: { createdAt: 'created_at', updatedAt: 'updated_at' } },
);

export const Clinic = mongoose.model<IClinic>('Clinic', ClinicSchema);

export interface IClinikoCreateIndividualAppointmentPayload {
  appointment_type_id: string;
  business_id: string;
  notes: string | null;
  patient_id: string;
  practitioner_id: string;
  starts_at: string;
}

export interface IClinikoCreatePatientPayload {
  date_of_birth: string;
  first_name: string;
  last_name: string;
  patient_phone_numbers: Array<{
    phone_type: 'Mobile';
    number: string;
  }>;
  address_1: string | null;
  /*address_2: string | null;
  address_3: string | null;*/
  sex: string | null;
}

export interface IClinikoUpdateIndividualAppointmentPayload {
  starts_at: string;
  notes: string | null;
  ends_at: string;
}

export interface IClinikoCancelIndividualAppointmentPayload {
  cancellation_note: string | null;
  cancellation_reason: number;
}

export interface IClinikoUpdatePatientPayload {
  title?: string;
  first_name?: string;
  last_name?: string;
  date_of_birth?: string;
  patient_phone_numbers?: {
    number: string;
    phone_type: 'Mobile' | 'Home' | 'Work' | 'Fax' | 'Other';
  }[];
}

export interface IClinikoAvailableTimesParams {
  business_id: string;
  practitioner_id: string;
  appointment_type_id: string;
  from: string;
  to: string;
}

import { AxiosError } from 'axios';
import express, { type Response } from 'express';
import HttpApiError from '../utils/http_api_error';
import { getGenericErrorResponse } from '../utils';
import {
  assignTwilioPhoneNumberToVoiceAgent,
  getAllAdmins,
  getAllUsers,
  listTwilioPhoneNumbers,
  registerAdmin,
  removeAdmin,
  updateTwilioPhoneNumber,
} from '../controllers/admin';
import {
  ImportPhoneNumberPayload,
  UpdatePhoneNumberPayload,
} from '../types/admin';
import {
  createOrUpdateClinicForAdmin,
  getAllClinics,
  getClinicOwners,
  getUserClinicDetails,
} from '../controllers/clinic_controller';
import { IClinic } from '../models/Clinic';
import { IApiRequest } from '../types';
import { listAllCallsForAClinic } from '../controllers/retell_call_controller';

const router = express.Router();

router.post(
  '/retell/assign-twilio-number',
  (req: IApiRequest, res: Response) => {
    if (!req.user) {
      res.status(401).json({ message: 'Unauthorized access' });
      return;
    }
    const body = req.body as ImportPhoneNumberPayload;
    assignTwilioPhoneNumberToVoiceAgent(req.user, body)
      .then((data) => {
        res.json({ data });
      })
      .catch((error: Error) => {
        const statusCode =
          (error as AxiosError).response?.status ??
          (error as HttpApiError).statusCode ??
          500;
        res.status(statusCode);
        const fallbackErrorResponse = getGenericErrorResponse();
        fallbackErrorResponse.error.message = error.message;
        res.json((error as AxiosError).response?.data ?? fallbackErrorResponse);
      });
  },
);

router.get('/twilio-phone-numbers', (req: IApiRequest, res: Response) => {
  listTwilioPhoneNumbers()
    .then((data) => {
      res.json({ data });
    })
    .catch((error: Error) => {
      const statusCode =
        (error as AxiosError).response?.status ??
        (error as HttpApiError).statusCode ??
        500;
      res.status(statusCode);
      const fallbackErrorResponse = getGenericErrorResponse();
      fallbackErrorResponse.error.message = error.message;
      res.json((error as AxiosError).response?.data ?? fallbackErrorResponse);
    });
});

router.get('/clinics', (req: IApiRequest, res: Response) => {
  if (!req.user) {
    res.status(401).json({ message: 'Unauthorized access' });
    return;
  }
  getAllClinics(req.user, {})
    .then((data) => {
      res.json({ data });
    })
    .catch((error: Error) => {
      const statusCode =
        (error as AxiosError).response?.status ??
        (error as HttpApiError).statusCode ??
        500;
      res.status(statusCode);
      const fallbackErrorResponse = getGenericErrorResponse();
      fallbackErrorResponse.error.message = error.message;
      res.json((error as AxiosError).response?.data ?? fallbackErrorResponse);
    });
});

router.post('/clinic', (req: IApiRequest, res: Response) => {
  if (!req.user) {
    res.status(401).json({ message: 'Unauthorized access' });
    return;
  }
  const body = req.body as Partial<IClinic>;
  createOrUpdateClinicForAdmin(req.user, body)
    .then((data) => {
      res.json({ data });
    })
    .catch((error: Error) => {
      const statusCode =
        (error as AxiosError).response?.status ??
        (error as HttpApiError).statusCode ??
        500;
      res.status(statusCode);
      const fallbackErrorResponse = getGenericErrorResponse();
      fallbackErrorResponse.error.message = error.message;
      res.json((error as AxiosError).response?.data ?? fallbackErrorResponse);
    });
});

router.get('/users', (req: IApiRequest, res: Response) => {
  if (!req.user) {
    res.status(401).json({ message: 'Unauthorized access' });
    return;
  }
  getAllUsers(req.user, {})
    .then((data) => {
      res.json({ data });
    })
    .catch((error: Error) => {
      const statusCode =
        (error as AxiosError).response?.status ??
        (error as HttpApiError).statusCode ??
        500;
      res.status(statusCode);
      const fallbackErrorResponse = getGenericErrorResponse();
      fallbackErrorResponse.error.message = error.message;
      res.json((error as AxiosError).response?.data ?? fallbackErrorResponse);
    });
});

router.post('/register-admin', (req: IApiRequest, res: Response) => {
  if (!req.user) {
    res.status(401).json({ message: 'Unauthorized access' });
    return;
  }
  const body = req.body;
  registerAdmin(req.user, body)
    .then((data) => {
      res.json({ data });
    })
    .catch((error: Error) => {
      const statusCode =
        (error as AxiosError).response?.status ??
        (error as HttpApiError).statusCode ??
        500;
      res.status(statusCode);
      const fallbackErrorResponse = getGenericErrorResponse();
      fallbackErrorResponse.error.message = error.message;
      res.json((error as AxiosError).response?.data ?? fallbackErrorResponse);
    });
});

router.post('/remove-admin', (req: IApiRequest, res: Response) => {
  if (!req.user) {
    res.status(401).json({ message: 'Unauthorized access' });
    return;
  }
  const body = req.body;
  removeAdmin(req.user, body)
    .then((data) => {
      res.json({ data });
    })
    .catch((error: Error) => {
      const statusCode =
        (error as AxiosError).response?.status ??
        (error as HttpApiError).statusCode ??
        500;
      res.status(statusCode);
      const fallbackErrorResponse = getGenericErrorResponse();
      fallbackErrorResponse.error.message = error.message;
      res.json((error as AxiosError).response?.data ?? fallbackErrorResponse);
    });
});

router.get('/admins', (req: IApiRequest, res: Response) => {
  if (!req.user) {
    res.status(401).json({ message: 'Unauthorized access' });
    return;
  }
  getAllAdmins(req.user)
    .then((data) => {
      res.json({ data });
    })
    .catch((error: Error) => {
      const statusCode =
        (error as AxiosError).response?.status ??
        (error as HttpApiError).statusCode ??
        500;
      res.status(statusCode);
      const fallbackErrorResponse = getGenericErrorResponse();
      fallbackErrorResponse.error.message = error.message;
      res.json((error as AxiosError).response?.data ?? fallbackErrorResponse);
    });
});

router.get('/analytics/list-calls', (req: IApiRequest, res: Response) => {
  const clinicId = req.query.clinic_id as string;
  if (!clinicId) {
    res.status(400).json({
      error: {
        message: 'clinic id must be provided',
      },
    });
    return;
  }

  if (!req.user) {
    res.status(401).json({ message: 'Unauthorized access' });
    return;
  }
  listAllCallsForAClinic(req.user, {
    clinicId: clinicId as string,
    startDate: req.query.start_date as string,
    endDate: req.query.end_date as string,
  })
    .then((data) => {
      res.json({ data });
    })
    .catch((error: Error) => {
      const statusCode =
        (error as AxiosError).response?.status ??
        (error as HttpApiError).statusCode ??
        500;
      res.status(statusCode);
      const fallbackErrorResponse = getGenericErrorResponse();
      fallbackErrorResponse.error.message = error.message;
      res.json((error as AxiosError).response?.data ?? fallbackErrorResponse);
    });
});

router.post(
  '/retell/update-phone-number',
  (req: IApiRequest, res: Response) => {
    if (!req.user) {
      res.status(401).json({ message: 'Unauthorized access' });
      return;
    }
    const body = req.body as UpdatePhoneNumberPayload;
    updateTwilioPhoneNumber(req.user, body)
      .then((data) => {
        res.json({ data });
      })
      .catch((error: Error) => {
        const statusCode =
          (error as AxiosError).response?.status ??
          (error as HttpApiError).statusCode ??
          500;
        res.status(statusCode);
        const fallbackErrorResponse = getGenericErrorResponse();
        fallbackErrorResponse.error.message = error.message;
        res.json((error as AxiosError).response?.data ?? fallbackErrorResponse);
      });
  },
);

router.get('/clinic-owners', (req: IApiRequest, res: Response) => {
  const clinicId = req.query.clinic_id as string;
  if (!clinicId) {
    res.status(400).json({
      error: {
        message: 'clinic id must be provided',
      },
    });
    return;
  }

  if (!req.user) {
    res.status(401).json({ message: 'Unauthorized access' });
    return;
  }
  getClinicOwners(req.user, clinicId)
    .then((data) => {
      res.json({ data });
    })
    .catch((error: Error) => {
      const statusCode =
        (error as AxiosError).response?.status ??
        (error as HttpApiError).statusCode ??
        500;
      res.status(statusCode);
      const fallbackErrorResponse = getGenericErrorResponse();
      fallbackErrorResponse.error.message = error.message;
      res.json((error as AxiosError).response?.data ?? fallbackErrorResponse);
    });
});

// deprecated, use db routes passing id token
router.get('/clinics/:id', (req: IApiRequest, res: Response) => {
  if (!req.user) {
    res.status(401).json({ message: 'Unauthorized access' });
    return;
  }

  const { id } = req.params;

  // Validate input
  if (!id) {
    res.status(400).json({
      error: {
        message: 'Mising clinic ID',
      },
    });
    return;
  }

  getUserClinicDetails(req.user, {
    clinicId: id as string,
  })
    .then((data) => {
      res.json({ data });
    })
    .catch((error: Error) => {
      const statusCode = (error as AxiosError).response?.status ?? 500;
      res.status(statusCode);
      const fallbackErrorResponse = getGenericErrorResponse();
      fallbackErrorResponse.error.message = error.message;
      res.json((error as AxiosError).response?.data ?? fallbackErrorResponse);
    });
});

export default router;

import { SendEmailCommandOutput } from '@aws-sdk/client-ses';
import { IUserInvite } from '../models/UserInvite';
import logger from '../modules/logger';
import { sendSimpleEmail } from '../modules/ses';

export const sendUserInviteEmail = async (userInvite: IUserInvite) => {
  const toEmail = userInvite.invitee_email;
  const inviterName = userInvite.inviter_name || 'Someone';
  const inviteUrl = `https://app.smartreception.ai/accept-invite?token=${userInvite.token}`;

  const params = {
    to: [toEmail],
    subject: `You’ve been invited to join SmartReception`,
    htmlBody: `
      <div style="font-family: sans-serif; color: #333;">
        <h2>Hi ${userInvite.invitee_name},</h2>
        <p><strong>${inviterName}</strong> has invited you to join their clinic on <strong>SmartReception</strong>.</p>
        <p>Click the button below to accept the invitation:</p>
        <a href="${inviteUrl}" style="display: inline-block; padding: 10px 20px; background-color: #2b7de9; color: white; text-decoration: none; border-radius: 4px;">
          Accept Invitation
        </a>
        <p style="margin-top: 20px;">If you don’t want to join, you can ignore this email.</p>
        <hr />
        <p style="font-size: 12px; color: #888;">This link will expire on ${new Date(userInvite.expires_at).toLocaleDateString()}.</p>
      </div>
    `,
  };

  try {
    const response = await sendSimpleEmail(params);
    logger.info(
      `Invite email sent to ${toEmail} with message id: ${response.MessageId}`,
    );
  } catch (error) {
    logger.error(`Failed to send invite email to ${toEmail}:`, error);
  }
};

export const sendUserSupportRequestEmail = async (payload: {
  email: string;
  first_name: string;
  subject: string;
  message: string;
}): Promise<SendEmailCommandOutput> => {
  const params = {
    to: ['<EMAIL>', payload.email], // Team + user
    subject: `Support Request Received: ${payload.subject}`,
    htmlBody: `
      <div style="font-family: sans-serif; color: #333;">
        <h2>Hi ${payload.first_name},</h2>
        <p>Thanks for contacting SmartReception support.</p>
        <p>We’ve received your request and our team will get back to you shortly.</p>
        <hr />
        <h3>Your Message:</h3>
        <p><strong>Subject:</strong> ${payload.subject}</p>
        <p>${payload.message}</p>
        <hr />
        <p style="font-size: 12px; color: #888;">
          If you have more details to add, please reply to this email.
        </p>
      </div>
    `,
  };

  try {
    const response = await sendSimpleEmail(params);
    logger.info(
      `Support request acknowledgment email sent to ${payload.email}, message ID: ${response.MessageId}`,
    );
    return response;
  } catch (error) {
    logger.error(
      `Failed to send support request email to ${payload.email}:`,
      error,
    );
    throw error;
  }
};

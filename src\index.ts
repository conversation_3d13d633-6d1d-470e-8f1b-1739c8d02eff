import express from 'express';
import cors from 'cors';
import axiosRetry from 'axios-retry';
import axios from 'axios';
import 'dotenv/config';

import logger from './modules/logger';
import routes from './routes';
import agentRoutes from './routes/agent_routes';
import retellRoutes from './routes/retell_routes';
import dbRoutes from './routes/db_routes';
import usersRoutes from './routes/users_routes';
import adminRoutes from './routes/admin_routes';
import { connectToDB } from './modules/docdb';
import { authMiddleware, adminOnlyMiddleware } from './middlewares';
import { checkAndCreateFirstAdmin } from './controllers/admin';
import migrate from './db-migration';

const app = express();
const port = process.env.PORT ?? 4000;

// setup retry on global axios
axiosRetry(axios, {
  retries: 3, // number of retries
  retryDelay: (retryCount) => {
    logger.info(`axios retry attempt: ${retryCount}`);
    return retryCount * 1000; // time interval between retries
  },
  retryCondition: (error) => {
    logger.info(`Inside retryCondition:`, error.response?.status);
    // if retry condition is not specified, by default idempotent requests are retried
    return error?.response?.status === 500;
  },
});

const corsOptions = {
  origin: [
    /^https?:\/\/([a-zA-Z0-9-]+\.)*smartreception\.ai$/,
    'http://localhost:3000',
  ],
  optionsSuccessStatus: 200, // some legacy browsers (IE11, various SmartTVs) choke on 204
};
app.use(cors(corsOptions));

app.use(
  express.json({
    limit: '5mb',
    verify: (req, res, buf) => {
      req.rawBody = buf.toString();
    },
  }),
);
// app.use(express.urlencoded({ extended: true, limit: '5mb' }));

app.use((req, res, next) => {
  if (['/api/ping'].includes(req.originalUrl)) {
    // do not log these requests
  } else {
    logger.info(
      'Request: %s %s, Trace-id: %s',
      req.method,
      req.originalUrl,
      req.headers['trace-id'],
    );
  }
  next();
});

// public routes
const BASE_PATH = '/api';

app.use(`${BASE_PATH}/`, routes);
app.use(`${BASE_PATH}/agent`, agentRoutes);

app.use(authMiddleware);
app.use(`${BASE_PATH}/db`, dbRoutes);
app.use(`${BASE_PATH}/retell`, retellRoutes);
app.use(`${BASE_PATH}/users`, usersRoutes);

app.use(adminOnlyMiddleware);
app.use(`${BASE_PATH}/admin`, adminRoutes);

(async () => {
  try {
    await connectToDB(); // Connect to MongoDB first
    app.listen(port, () => {
      logger.info(`[server]: Server is Running at http://localhost:${port}`);
    });
    // create first admin if not already exist
    void checkAndCreateFirstAdmin();
    void migrate(); // todo clean
  } catch (err) {
    logger.error('❌ Failed to start server due to DB connection error:', err);
    process.exit(1); // Exit process if DB fails
  }
})();

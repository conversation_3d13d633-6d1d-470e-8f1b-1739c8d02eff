export interface ICreateKbPayload {
  urls: string[];
  enableAutoRefresh?: boolean;
  files: Express.Multer.File[];
  clinicId: string;
  texts: Array<{
    text: string;
    title: string;
  }>;
}

export interface IGetKnowledgeBasePayload {
  clinicId: string;
}

export interface IDeleteKnowledgeBasePayload {
  knowledgeBaseId: string;
  clinicId: string;
}

export interface ICreateVoiceAgentPayload {
  clinicId: string;
  pronunciationDict?: Array<{
    word: string;
    alphabet: 'ipa' | 'cmu';
    phoneme: string;
  }>;
}

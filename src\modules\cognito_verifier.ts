import { CognitoJwtVerifier } from 'aws-jwt-verify';

export const cognitoAccessTokenVerifier = CognitoJwtVerifier.create({
  userPoolId: process.env.COGNITO_USER_POOL_ID ?? '',
  clientId: process.env.COGNITO_USER_POOL_CLIENT_ID ?? '',
  tokenUse: 'access', // Important! Access token for backend auth
});

export const cognitoIdTokenVerifier = CognitoJwtVerifier.create({
  userPoolId: process.env.COGNITO_USER_POOL_ID ?? '',
  clientId: process.env.COGNITO_USER_POOL_CLIENT_ID ?? '',
  tokenUse: 'id', // important: 'id' or 'access'
});

import mongoose, { Schema, Document } from 'mongoose';

// Creating Users table in db and not relying on Cognito: for customization, analytics, faster queries etc
export interface IUser extends Document {
  email: string;
  mobile?: string;
  country_code?: string;
  first_name?: string;
  last_name?: string;
  uid: string; // cognito username
  created_at: Date;
  updated_at: Date;
  last_login?: Date;
}

const UserSchema = new Schema<IUser>(
  {
    first_name: { type: String, required: false },
    last_name: { type: String, required: false },
    email: { type: String, required: true, unique: true },
    uid: { type: String, required: true, unique: true },
    mobile: { type: String, required: false, unique: true, sparse: true },
    country_code: { type: String, required: false },
    last_login: { type: Date, required: false },
  },
  { timestamps: { createdAt: 'created_at', updatedAt: 'updated_at' } },
);

export const User = mongoose.model<IUser>('User', UserSchema);

class HttpApiError extends Error {
  statusCode: number;

  constructor(statusCode: number, message: string) {
    super(message); // Call super with the error message
    this.statusCode = statusCode;
    this.name = 'HttpApiError'; // Set the name property for better identification
    Object.setPrototypeOf(this, HttpApiError.prototype); // Ensure prototype chain is correctly set
  }
}

export default HttpApiError;

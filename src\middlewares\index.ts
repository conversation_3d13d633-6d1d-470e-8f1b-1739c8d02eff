import { Response, NextFunction } from 'express';
import auth from '../modules/auth';
import { AdminPrivilegeLevel, IApiRequest, IAuthUser } from '../types';
import logger from '../modules/logger';

export const authMiddleware = async (
  req: IApiRequest,
  res: Response,
  next: NextFunction,
): Promise<void> => {
  const authHeader = req.headers['authorization'];
  const token = authHeader?.split(' ')[1]; // Bearer <token>

  if (!token) {
    res.status(401).json({ message: 'Token not provided' });
    return;
  }

  try {
    const decoded = await auth.verifyCognitoAccessToken(token);
    const groups = decoded['cognito:groups'] || [];
    if (groups.includes('admin')) {
      req.user = {
        uid: decoded.username,
        role: 'admin',
      };

      // some non admin APIs, e.g. retell Agent & KB CRUD are also available to admins
      const idToken = req.headers['x-id-token'] as string;
      if (idToken) {
        await populateAdminPrivilegeLevel(req.user, idToken);
      }
    } else {
      req.user = {
        uid: decoded.username,
        role: 'user',
      };
    }
    logger.debug(decoded);
    logger.info(req.user);
    next();
  } catch (error) {
    logger.error(error);
    res.status(403).json({
      error: {
        message: 'Token is invalid',
      },
    });
    return;
  }
};

// pass id token in x-id-token header
export const adminOnlyMiddleware = async (
  req: IApiRequest,
  res: Response,
  next: NextFunction,
): Promise<void> => {
  if (req.user?.role !== 'admin') {
    res.status(403).json({
      error: {
        message: 'Admin access only',
      },
    });
    return;
  }

  const idToken = req.headers['x-id-token'] as string;
  if (!idToken) {
    res.status(401).json({
      error: {
        message: 'x-id-token header missing for admin access',
      },
    });
    return;
  }

  try {
    await populateAdminPrivilegeLevel(req.user, idToken);
    logger.info(req.user);
    next();
  } catch (error) {
    logger.error('Failed to verify ID token for admin', error);
    res.status(403).json({
      error: {
        message: 'Invalid admin ID token',
      },
    });
  }
};

const populateAdminPrivilegeLevel = async (
  authUser: IAuthUser,
  idToken: string,
) => {
  const decodedIdToken = await auth.verifyCognitoIdToken(idToken);
  const adminPrivilegeLevel = decodedIdToken[
    'custom:admin_level'
  ] as AdminPrivilegeLevel;

  authUser.admin = {
    ...authUser.admin,
    privilege_level: adminPrivilegeLevel,
  };
};

//import { UserClinicRole } from '../models/UserClinicRole';
//import { User } from '../models/User';

const migrate = async () => {
  //const result = await UserClinicRole.updateMany({}, { $set: { user_id: '892e1408-e0d1-7062-4bbc-d5be0b6e1d5b' } });
  /*const result = await UserClinicRole.updateMany(
    {
      clinic_id: {
        $in: ['6822e9604b8b521b826a0528', '6822e8a34b8b521b826a0521'],
      },
    },
    {
      $set: {
        user_id: '892e1408-e0d1-7062-4bbc-d5be0b6e1d5b',
      },
    },
  );
  console.log(`🔁 Updated ${result.deletedCount} UserClinicRole documents`);*/
  /*const result = await User.deleteMany({});
  console.log(`🧹 Deleted ${result.deletedCount} users from the database.`);*/
};

export default migrate;

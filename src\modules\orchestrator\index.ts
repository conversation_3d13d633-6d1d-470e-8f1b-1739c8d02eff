import axios from 'axios';
import { IClinic } from '../../models/Clinic';
import { CrmSystems } from '../../types';
import {
  IBookAppointmentForExistingPatientArgs,
  IBookAppointmentForNewPatientArgs,
  ICancelAppointmentArgs,
  IGetAvailableAppointmentSlotsArgs,
  IGetPatientAppointmentDetailsArgs,
  IGetPatientDetailsArgs,
  IGetPractitionersListArgs,
  IGetPatientReferralSourceArgs,
  IRescheduleAppointmentArgs,
  IUpdatePatientDetailsArgs,
} from '../../types/agent';
import logger from '../logger';
import cliniko from './cliniko';
import {
  IClinikoAvailableTimesParams,
  IClinikoCancelIndividualAppointmentPayload,
  IClinikoCreateIndividualAppointmentPayload,
  IClinikoCreatePatientPayload,
  IClinikoUpdateIndividualAppointmentPayload,
  IClinikoUpdatePatientPayload,
} from './cliniko/types';
import { convertDateIntoAest } from '../../utils';

const getClinikoBasicAuthHeader = (apiKey: string): string => {
  // Create the auth string in the format "username:password"
  // For Cliniko, username is the API key and password is empty
  const authString = `${apiKey}:`;

  // Convert to Base64
  const base64Auth = Buffer.from(authString).toString('base64');

  // Return the properly formatted Authorization header
  return `Basic ${base64Auth}`;
};

export const bookAppointmentForNewPatient = async (
  businessDetails: Partial<IClinic>,
  args: IBookAppointmentForNewPatientArgs,
): Promise<object> => {
  const crmSystem = businessDetails.crm_details?.name;

  if (crmSystem === CrmSystems.CLINIKO) {
    const clinikoApiKey =
      businessDetails.crm_details?.auth_details?.api_key ?? '';
    const clinikoBusinessid = args.business_location_id ?? '';
    const clinikoAppointmentTypeId =
      businessDetails.crm_details?.custom_fields?.appointment_type_id ?? '';

    const headers = {
      Authorization: getClinikoBasicAuthHeader(clinikoApiKey),
    };

    try {
      // 1. First create new patient
      const patientPayload: IClinikoCreatePatientPayload = {
        first_name: args.patient_first_name,
        last_name: args.patient_last_name,
        date_of_birth: args.patient_dob,
        patient_phone_numbers: [
          {
            phone_type: 'Mobile',
            number: args.patient_mobile,
          },
        ],
        address_1: args.patient_address ?? null,
        sex: args.patient_gender ?? null,
      };

      const patient = await cliniko.createPatient({
        payload: patientPayload,
        headers,
      });

      // 2. Then book appointment
      const appointmentPayload: IClinikoCreateIndividualAppointmentPayload = {
        appointment_type_id: clinikoAppointmentTypeId,
        business_id: clinikoBusinessid,
        patient_id: patient.id, // Using the ID from created patient
        practitioner_id: args.practitioner_id,
        starts_at: args.appointment_date,
        notes: args.reason_for_visit || null,
      };

      const appointment = await cliniko.createIndividualAppointment({
        payload: appointmentPayload,
        headers,
      });

      return {
        patient,
        appointment,
      };
    } catch (error) {
      logger.error('Failed to book appointment for new patient:', error);
      throw error;
    }
  }

  throw new Error(`Unsupported CRM System: ${crmSystem}`);
};

export const bookAppointmentForExistingPatient = async (
  businessDetails: Partial<IClinic>,
  args: IBookAppointmentForExistingPatientArgs,
): Promise<object> => {
  const crmSystem = businessDetails.crm_details?.name;
  if (crmSystem === CrmSystems.CLINIKO) {
    logger.debug(args);
    // todo
    const clinikoApiKey =
      businessDetails.crm_details?.auth_details?.api_key ?? '';
    const clinikoBusinessid = args.business_location_id ?? '';
    const clinikoAppointmentTypeId =
      businessDetails.crm_details?.custom_fields?.appointment_type_id ?? '';

    const headers = {
      Authorization: getClinikoBasicAuthHeader(clinikoApiKey),
    };
    try {
      const appointmentPayload: IClinikoCreateIndividualAppointmentPayload = {
        appointment_type_id: clinikoAppointmentTypeId,
        business_id: clinikoBusinessid,
        patient_id: args.patient_id, // Using the ID from created patient
        practitioner_id: args.practitioner_id,
        starts_at: args.appointment_date,
        notes: args.reason_for_visit || null,
      };

      const appointment = await cliniko.createIndividualAppointment({
        payload: appointmentPayload,
        headers,
      });

      return {
        appointment,
      };
    } catch (error) {
      logger.error('Failed to book appointment for existing patient:', error);
      throw error;
    }
  }
  throw new Error(`Unsupported CRM System: ${crmSystem}`);
};

export const rescheduleAppointment = async (
  businessDetails: Partial<IClinic>,
  args: IRescheduleAppointmentArgs,
): Promise<object> => {
  const crmSystem = businessDetails.crm_details?.name;
  if (crmSystem === CrmSystems.CLINIKO) {
    logger.debug(args);
    // todo
    const clinikoApiKey =
      businessDetails.crm_details?.auth_details?.api_key ?? '';
    const headers = {
      Authorization: getClinikoBasicAuthHeader(clinikoApiKey),
    };
    try {
      const updateAppointmentPayload: IClinikoUpdateIndividualAppointmentPayload =
        {
          starts_at: args.new_appointment_starts_at,
          notes: args.reschedule_reason ?? null,
          ends_at: args.new_appointment_ends_at,
        };
      const appointment = await cliniko.updateIndividualAppointment({
        appointment_id: args.appointment_id,
        payload: updateAppointmentPayload,
        headers,
      });

      return {
        appointment,
      };
    } catch (error) {
      logger.error('Failed to reschedule appointment:', error);
      throw error;
    }
  }
  throw new Error(`Unsupported CRM System: ${crmSystem}`);
};

export const cancelAppointment = async (
  businessDetails: Partial<IClinic>,
  args: ICancelAppointmentArgs,
): Promise<object> => {
  const crmSystem = businessDetails.crm_details?.name;
  if (crmSystem === CrmSystems.CLINIKO) {
    logger.debug(args);
    // todo
    const clinikoApiKey =
      businessDetails.crm_details?.auth_details?.api_key ?? '';
    const headers = {
      Authorization: getClinikoBasicAuthHeader(clinikoApiKey),
    };
    try {
      //https://docs.api.cliniko.com/openapi/individual-appointment/cancelindividualappointment-patch
      const cancelIndividualAppointmentPayload: IClinikoCancelIndividualAppointmentPayload =
        {
          cancellation_note: args.cancellation_reason ?? null,
          cancellation_reason: 50, //others
        };
      const cancelled_appointment = await cliniko.cancelIndividualAppointment({
        appointment_id: args.appointment_id,
        payload: cancelIndividualAppointmentPayload,
        headers,
      });

      return {
        cancelled_appointment,
      };
    } catch (error) {
      logger.error('Failed to cancel appointment:', error);
      throw error;
    }
  }
  throw new Error(`Unsupported CRM System: ${crmSystem}`);
};

export const getPatientDetails = async (
  businessDetails: Partial<IClinic>,
  args: IGetPatientDetailsArgs,
): Promise<object> => {
  const crmSystem = businessDetails.crm_details?.name;
  if (crmSystem === CrmSystems.CLINIKO) {
    logger.debug(args);
    // todo
    const clinikoApiKey =
      businessDetails.crm_details?.auth_details?.api_key ?? '';
    const headers = {
      Authorization: getClinikoBasicAuthHeader(clinikoApiKey),
    };
    try {
      const filters: string[] = [];
      if (args.patient_dob) {
        filters.push(
          `q[]=${encodeURIComponent(`date_of_birth:=${args.patient_dob}`)}`,
        );
      }
      if (args.patient_first_name) {
        filters.push(
          `q[]=${encodeURIComponent(`first_name:=${args.patient_first_name}`)}`,
        );
      }
      if (args.patient_last_name) {
        filters.push(
          `q[]=${encodeURIComponent(`last_name:=${args.patient_last_name}`)}`,
        );
      }
      filters.push('sort=created_at:desc');
      filters.push('page=1');
      filters.push('per_page=100');
      const filtersForQuery: string = filters.join('&');
      const response = (await cliniko.getPatient({
        filtersForQuery,
        headers,
      })) as { patients: object[] };

      // if there are no results, search just by dob since Agent might pick up wronmg spelling in first/last name
      /*if (!response.patients?.length && args.patient_dob) {
        logger.info('Using dobOnlyFilters to fetch getPatient');
        const dobOnlyFilters = [
          `q[]=${encodeURIComponent(`date_of_birth:=${args.patient_dob}`)}`,
          'sort=created_at:desc',
          'page=1',
          'per_page=100',
        ];
        response = (await cliniko.getPatient({
          filtersForQuery: dobOnlyFilters.join('&'),
          headers,
        })) as { patients: object[] };
      }*/

      return {
        patients: response?.patients,
      };
    } catch (error) {
      logger.error(error);
      throw error;
    }
  }
  throw new Error(`Unsupported CRM System: ${crmSystem}`);
};

export const getPatientAppointmentDetails = async (
  businessDetails: Partial<IClinic>,
  args: IGetPatientAppointmentDetailsArgs,
): Promise<object> => {
  const crmSystem = businessDetails.crm_details?.name;
  if (crmSystem === CrmSystems.CLINIKO) {
    logger.debug(args);
    // todo
    const clinikoApiKey =
      businessDetails.crm_details?.auth_details?.api_key ?? '';
    const headers = {
      Authorization: getClinikoBasicAuthHeader(clinikoApiKey),
    };
    try {
      const filters: string[] = [];
      filters.push(
        `q[]=${encodeURIComponent(`patient_id:=${args.patient_id}`)}`,
      );
      filters.push('sort=created_at:desc');
      filters.push('page=1');
      filters.push('per_page=100');
      const filtersForQuery: string = filters.join('&');
      const appointments = await cliniko.getAppointments({
        filtersForQuery,
        headers,
      });
      return {
        appointments,
      };
    } catch (error) {
      logger.error(error);
      throw error;
    }
  }
  throw new Error(`Unsupported CRM System: ${crmSystem}`);
};

export const updatePatientDetails = async (
  businessDetails: Partial<IClinic>,
  args: IUpdatePatientDetailsArgs,
): Promise<object> => {
  const crmSystem = businessDetails.crm_details?.name;
  if (crmSystem === CrmSystems.CLINIKO) {
    logger.debug(args);
    // todo
    const clinikoApiKey =
      businessDetails.crm_details?.auth_details?.api_key ?? '';
    const headers = {
      Authorization: getClinikoBasicAuthHeader(clinikoApiKey),
    };
    try {
      const updatePatientPayload: IClinikoUpdatePatientPayload = {
        ...(args.patient_title && { title: args.patient_title }),
        ...(args.patient_first_name && { first_name: args.patient_first_name }),
        ...(args.patient_last_name && { last_name: args.patient_last_name }),
        ...(args.patient_dob && { date_of_birth: args.patient_dob }),
        ...(args.patient_mobile && {
          patient_phone_numbers: [
            { number: args.patient_mobile, phone_type: 'Mobile' },
          ],
        }),
      };
      const patient = await cliniko.updatePatient({
        patient_id: args.patient_id,
        payload: updatePatientPayload,
        headers,
      });
      return {
        patient,
      };
    } catch (error) {
      logger.error(error);
      throw error;
    }
  }
  throw new Error(`Unsupported CRM System: ${crmSystem}`);
};

export const getAvailableAppointmentSlots = async (
  businessDetails: Partial<IClinic>,
  args: IGetAvailableAppointmentSlotsArgs,
): Promise<object> => {
  const crmSystem = businessDetails.crm_details?.name;
  if (crmSystem === CrmSystems.CLINIKO) {
    logger.debug(args);
    // todo
    const clinikoApiKey =
      businessDetails.crm_details?.auth_details?.api_key ?? '';
    const clinikoBusinessid = args.business_location_id ?? '';
    const clinikoAppointmentTypeId =
      businessDetails.crm_details?.custom_fields?.appointment_type_id ?? '';
    const headers = {
      Authorization: getClinikoBasicAuthHeader(clinikoApiKey),
    };
    try {
      const getAllAvailableTimesParams: IClinikoAvailableTimesParams = {
        business_id: clinikoBusinessid,
        practitioner_id: args.practitioner_id,
        appointment_type_id: clinikoAppointmentTypeId,
        from: args.from_date.split('T')[0],
        to: args.to_date.split('T')[0],
      };
      // Online bookings must be enabled in Cliniko settings. The business, appointment type, and practitioner must also be enabled for online bookings — otherwise, the API may return 404 not found.
      const slots = await cliniko.getAllAvailableTimes({
        availableTimeParams: getAllAvailableTimesParams,
        headers,
      });
      // append appointment_start_aest so that agent can handle relative dates in Australian time zone
      if (slots?.available_times?.length) {
        slots.available_times.forEach((slot) => {
          slot.appointment_start_aest = convertDateIntoAest(
            new Date(slot.appointment_start),
          );
        });
      }
      return {
        slots,
      };
    } catch (error) {
      logger.error(error);
      throw error;
    }
  }
  throw new Error(`Unsupported CRM System: ${crmSystem}`);
};

export const getPractitionersList = async (
  businessDetails: Partial<IClinic>,
  args: IGetPractitionersListArgs,
): Promise<object> => {
  const crmSystem = businessDetails.crm_details?.name;
  if (crmSystem === CrmSystems.CLINIKO) {
    logger.debug(args);
    // todo
    const clinikoApiKey =
      businessDetails.crm_details?.auth_details?.api_key ?? '';
    const headers = {
      Authorization: getClinikoBasicAuthHeader(clinikoApiKey),
    };
    try {
      const result = await cliniko.getPractitioners({
        headers,
      });
      return result;
    } catch (error) {
      logger.error(error);
      throw error;
    }
  }
  throw new Error(`Unsupported CRM System: ${crmSystem}`);
};

interface IClinikoPatientCase {
  id: string;
  contact?: {
    links?: {
      self?: string;
    };
  };
}
interface IGetPatientCasesResponse {
  patient_cases: IClinikoPatientCase[];
}

// Then keep your `getPatientReferralSource` function below this
export const getPatientReferralSource = async (
  businessDetails: Partial<IClinic>,
  args: IGetPatientReferralSourceArgs,
): Promise<object> => {
  const crmSystem = businessDetails.crm_details?.name;
  if (crmSystem === CrmSystems.CLINIKO) {
    const clinikoApiKey =
      businessDetails.crm_details?.auth_details?.api_key ?? '';

    const headers = {
      Authorization: getClinikoBasicAuthHeader(clinikoApiKey),
      Accept: 'application/json',
    };

    const patientId = args.patient_id;
    const filtersForQuery = `q[]=patient_id:=${patientId}`;

    const patientCases = (await cliniko.getPatientActiveCases({
      filtersForQuery,
      headers,
    })) as IGetPatientCasesResponse;

    const enrichedPatientCases = await Promise.all(
      patientCases.patient_cases.map(async (pc) => {
        let referringDoctorName = null;

        if (pc?.contact?.links?.self) {
          try {
            const contactResponse = await axios.get(pc.contact.links.self, {
              headers,
            });

            const contact = contactResponse.data;
            referringDoctorName =
              `${contact.first_name ?? ''} ${contact.last_name ?? ''}`.trim();
          } catch (error) {
            logger.error(
              'Error fetching referring doctor for case ID:',
              pc.id,
              error,
            );
          }
        }

        return {
          ...pc,
          referring_doctor_name: referringDoctorName,
        };
      }),
    );

    return {
      patient_referrals: enrichedPatientCases,
    };
  }
  throw new Error(`Unsupported CRM System: ${crmSystem}`);
};

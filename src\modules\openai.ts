import axios, { type AxiosError } from 'axios';
import logger from './logger';

const OPEN_AI_API_KEY = process.env.OPEN_AI_API_KEY ?? '';

// Docs: https://platform.openai.com/docs/api-reference/chat/create
// GPT-3.5 costing: $0.0005 / 1K tokens(input), $0.0015 / 1K tokens (output), 16K context
// GPT-4 costing: 	$0.01 / 1K tokens (input), $0.03 / 1K tokens (output), 128K context
export const getResponseFromOpenAi = async (
  prompt: string,
  params?: {
    // 0 to 1, 0 being deterministic, 1 being random
    temperature?: number;
  },
): Promise<object> => {
  const messages = [
    {
      content: prompt,
      role: 'system',
    },
  ];
  const data = {
    model: 'gpt-4o-mini',
    messages,
    response_format: {
      // when using JSON mode, you must also instruct the model to produce JSON yourself via a system or user message
      type: 'json_object',
    },
    ...params,
  };
  const url = 'https://api.openai.com/v1/chat/completions';
  try {
    const response = await axios.post(url, data, {
      headers: {
        Authorization: `Bearer ${OPEN_AI_API_KEY}`,
      },
    });
    const content = response.data.choices?.[0]?.message?.content;
    return JSON.parse(content);
  } catch (error) {
    logger.error((error as AxiosError).response?.data);
    throw error;
  }
};

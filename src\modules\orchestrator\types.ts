/*// Patient Type
export interface IPatient {
  id: string;
  address: string[]; // Allows multiple address lines
  mobile: string;
  first_name: string;
  last_name: string;
  dob: string; // ISO date string (e.g., "1990-01-15")
  gender: 'male' | 'female' | 'other';
  email?: string; // Optional field
  custom_fields?: Record<string, string>; // For storing miscellaneous information
  created_at: string; // ISO date string
  updated_at: string; // ISO date string
  active: boolean;
}

// Appointment Type
export interface IAppointment {
  id: string;
  date: string; // ISO 8601 format (e.g., "2025-04-10T15:30:00Z")
  patient_id: string;
  practitioner_id: string;
  reason_for_visit: string;
  status: 'booked' | 'cancelled' | 'rescheduled';
  cancellation?: {
    cancelled_at: string;
    cancellation_reason: string;
  };
  reschedule?: {
    reschedule_reason: string;
    existing_appointment_date: string;
    new_appointment_date: string;
    rescheduled_at: string;
  }[];
  custom_fields?: Record<string, string>;
  created_at: string;
  updated_at: string;
}

// Practitioner Type
export interface IPractitioner {
  id: string;
  first_name: string;
  last_name: string;
  dob: string; // ISO date string
  gender: 'male' | 'female' | 'other';
  mobile: string;
  email?: string; // Optional field
  designation: string; // e.g., "Doctor", "Nurse", "Therapist"
  custom_fields?: Record<string, string>;
  created_at: string;
  updated_at: string;
  active: boolean;
}*/

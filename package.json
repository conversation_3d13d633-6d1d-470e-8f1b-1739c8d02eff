{"name": "smart-reception-be", "version": "1.0.0", "description": "Smart Reception Backend", "main": "dist", "scripts": {"start": "node dist", "lint": "eslint 'src/**/*.ts'", "dev": "cross-env NODE_OPTIONS=--max-old-space-size=4096 NODE_ENV=development concurrently \"tsc --watch\" \"nodemon dist\"", "build": "NODE_OPTIONS=--max-old-space-size=4096 tsc -p tsconfig.json && npm run copy:static", "copy:static": "cp -r src/modules/docdb/global-bundle.pem dist/modules/docdb/global-bundle.pem", "test": "echo \"Error: no test specified\" && exit 1", "format": "prettier --write .", "prepare": "husky install"}, "repository": {"type": "git", "url": "git+https://github.com/Smart-reception/smart-reception-be.git"}, "author": "", "license": "ISC", "bugs": {"url": "https://github.com/Smart-reception/smart-reception-be/issues"}, "homepage": "https://github.com/Smart-reception/smart-reception-be#readme", "devDependencies": {"@types/bunyan": "^1.8.11", "@types/cors": "^2.8.17", "@types/express": "^5.0.1", "@types/jsonwebtoken": "^9.0.9", "@types/mongodb": "^4.0.7", "@types/multer": "^1.4.12", "@types/node": "^22.14.1", "@typescript-eslint/eslint-plugin": "^8.29.1", "@typescript-eslint/parser": "^8.29.1", "concurrently": "^9.1.2", "cross-env": "^7.0.3", "eslint": "^9.24.0", "husky": "^9.1.7", "lint-staged": "^15.5.1", "nodemon": "^3.1.9", "prettier": "^3.5.3", "ts-node": "^10.9.2", "typescript": "^5.8.3", "typescript-eslint": "^8.29.1"}, "lint-staged": {"*.{js,ts}": "eslint --cache --fix", "*.{ts,js,css,md}": "prettier --write"}, "dependencies": {"@aws-sdk/client-bedrock-runtime": "^3.844.0", "@aws-sdk/client-cognito-identity-provider": "^3.821.0", "@aws-sdk/client-ses": "^3.844.0", "aws-jwt-verify": "^5.1.0", "axios": "^1.8.4", "axios-retry": "^4.5.0", "bunyan": "^1.8.15", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^5.1.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.14.1", "multer": "^1.4.5-lts.2", "nanoid": "3.0.0", "retell-sdk": "^4.37.0", "twilio": "^5.6.1"}}
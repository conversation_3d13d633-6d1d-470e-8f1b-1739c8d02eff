import { UserClinicRole } from '../models/UserClinicRole';
import {
  AdminPrivilegeLevel,
  IAuthUser,
  IGenericErrorResponse,
} from '../types';
import HttpApiError from './http_api_error';

export const getGenericErrorResponse = (): IGenericErrorResponse => {
  return {
    error: {
      message: 'Something went wrong',
    },
  };
};

export const hasUserAccessToThisClinic = async (
  user: IAuthUser,
  clinicId: string,
): Promise<boolean> => {
  //todo-clinic isActive or not
  const mapping = await UserClinicRole.findOne({
    user_id: user.uid,
    clinic_id: clinicId,
  });

  return !!mapping; // true if mapping exists, false otherwise
};

export const checkIfClinicUpdateIsAllowedForAdmin = async (user: IAuthUser) => {
  if (
    !user?.admin?.privilege_level ||
    ![AdminPrivilegeLevel.OWNER, AdminPrivilegeLevel.EDITOR].includes(
      user?.admin?.privilege_level,
    )
  ) {
    throw new HttpApiError(403, 'You do not have privilege for this operation');
  }
};

export const checkIfClinicUpdateIsAllowed = async (
  user: IAuthUser,
  clinicId: string,
): Promise<void> => {
  if (user?.role === 'admin') {
    await checkIfClinicUpdateIsAllowedForAdmin(user);
  } else {
    if (!(await hasUserAccessToThisClinic(user, clinicId))) {
      throw new HttpApiError(403, 'You do not have access to this clinic ');
    }
  }
};

// support for only limited countries
export const getParsedMobileObject = (
  mobile: string,
): { country_code: string; number: string } => {
  const VALID_COUNTRY_CODES = ['+91', '+61', '+44', '+1'];

  const code = VALID_COUNTRY_CODES.find((code) => mobile.startsWith(code));

  return {
    country_code: code ?? '',
    number: code ? mobile.slice(code.length) : mobile,
  };
};

export const convertDateIntoAest = (date: Date): string => {
  return new Date(date).toLocaleString('en-AU', {
    timeZone: 'Australia/Sydney',
    weekday: 'short', // "Fri"
    day: '2-digit', // "11"
    month: 'short', // "Jul"
    year: 'numeric', // "2025"
    hour: '2-digit',
    minute: '2-digit',
    hour12: true, // AM/PM format
  });
};

import { AxiosError } from 'axios';
import express, { type Response } from 'express';
import HttpApiError from '../utils/http_api_error';
import { getGenericErrorResponse } from '../utils';
import multerUpload from '../modules/multer';
import { ICreateKbPayload, ICreateVoiceAgentPayload } from '../types/retell';
import {
  createVoiceAgent,
  deleteOneKnowledgeBase,
  getAllKnowledgeBases,
  getClinicAgentDetails,
  listCallsForAClinic,
  uploadAndMapKnowledgeBase,
} from '../controllers/retell';
import { IApiRequest } from '../types';

const router = express.Router();

router.post(
  '/knowledge-bases',
  multerUpload.array('files', 10),
  (req: IApiRequest, res: Response) => {
    const { clinic_id: clinicId } = req.query;
    if (!clinicId) {
      res.status(400).json({
        error: {
          message: 'clinic_id must be provided',
        },
      });
      return;
    }

    if (!req.user) {
      res.status(401).json({ message: 'Unauthorized access' });
      return;
    }

    const payload: ICreateKbPayload = {
      urls: req.body?.urls ?? [],
      enableAutoRefresh: req.body?.enable_auto_refresh === true,
      files: (req.files as Express.Multer.File[]) ?? [],
      clinicId: clinicId as string,
      texts: req.body?.texts ?? [],
    };

    uploadAndMapKnowledgeBase(req.user, payload)
      .then((data) => {
        res.json({ data });
      })
      .catch((error: Error) => {
        const statusCode =
          (error as AxiosError).response?.status ??
          (error as HttpApiError).statusCode ??
          500;
        res.status(statusCode);
        const fallbackErrorResponse = getGenericErrorResponse();
        fallbackErrorResponse.error.message = error.message;
        res.json((error as AxiosError).response?.data ?? fallbackErrorResponse);
      });
  },
);

router.post('/voice-agent', (req: IApiRequest, res: Response) => {
  const { clinic_id: clinicId } = req.query;
  if (!clinicId) {
    res.status(400).json({
      error: {
        message: 'clinic id must be provided',
      },
    });
    return;
  }

  if (!req.user) {
    res.status(401).json({ message: 'Unauthorized access' });
    return;
  }

  const payload: ICreateVoiceAgentPayload = {
    pronunciationDict: req.body?.pronunciation_dict ?? [],
    clinicId: clinicId as string,
  };

  createVoiceAgent(req.user, payload)
    .then((data) => {
      res.json({ data });
    })
    .catch((error: Error) => {
      const statusCode =
        (error as AxiosError).response?.status ??
        (error as HttpApiError).statusCode ??
        500;
      res.status(statusCode);
      const fallbackErrorResponse = getGenericErrorResponse();
      fallbackErrorResponse.error.message = error.message;
      res.json((error as AxiosError).response?.data ?? fallbackErrorResponse);
    });
});

router.get('/agent', (req: IApiRequest, res: Response) => {
  const { clinic_id: clinicId } = req.query;
  if (!clinicId) {
    res.status(400).json({
      error: {
        message: 'clinic id must be provided',
      },
    });
    return;
  }

  if (!req.user) {
    res.status(401).json({ message: 'Unauthorized access' });
    return;
  }

  getClinicAgentDetails(req.user, {
    clinicId: clinicId as string,
  })
    .then((data) => {
      res.json({ data });
    })
    .catch((error: Error) => {
      const statusCode =
        (error as AxiosError).response?.status ??
        (error as HttpApiError).statusCode ??
        500;
      res.status(statusCode);
      const fallbackErrorResponse = getGenericErrorResponse();
      fallbackErrorResponse.error.message = error.message;
      res.json((error as AxiosError).response?.data ?? fallbackErrorResponse);
    });
});

router.get('/knowledge-bases', (req: IApiRequest, res: Response) => {
  const clinicId = req.query.clinic_id as string;
  if (!clinicId) {
    res.status(400).json({
      error: {
        message: 'clinic id must be provided',
      },
    });
    return;
  }

  if (!req.user) {
    res.status(401).json({ message: 'Unauthorized access' });
    return;
  }

  getAllKnowledgeBases(req.user, {
    clinicId: clinicId as string,
  })
    .then((data) => {
      res.json({ data });
    })
    .catch((error: Error) => {
      const statusCode =
        (error as AxiosError).response?.status ??
        (error as HttpApiError).statusCode ??
        500;
      res.status(statusCode);
      const fallbackErrorResponse = getGenericErrorResponse();
      fallbackErrorResponse.error.message = error.message;
      res.json((error as AxiosError).response?.data ?? fallbackErrorResponse);
    });
});

router.delete('/knowledge-bases/:id', (req: IApiRequest, res: Response) => {
  const clinicId = req.query.clinic_id as string;
  if (!clinicId) {
    res.status(400).json({
      error: {
        message: 'clinic id must be provided',
      },
    });
    return;
  }

  const { id } = req.params;

  if (!id) {
    res.status(400).json({
      error: {
        message: 'Missing id',
      },
    });
  }

  if (!req.user) {
    res.status(401).json({ message: 'Unauthorized access' });
    return;
  }

  deleteOneKnowledgeBase(req.user, {
    clinicId: clinicId as string,
    knowledgeBaseId: id,
  })
    .then((data) => {
      res.json({ data });
    })
    .catch((error: Error) => {
      const statusCode =
        (error as AxiosError).response?.status ??
        (error as HttpApiError).statusCode ??
        500;
      res.status(statusCode);
      const fallbackErrorResponse = getGenericErrorResponse();
      fallbackErrorResponse.error.message = error.message;
      res.json((error as AxiosError).response?.data ?? fallbackErrorResponse);
    });
});

// use /db/analytics/list-calls, this is for debugging, todo: clean later
router.get('/analytics/list-calls', (req: IApiRequest, res: Response) => {
  const clinicId = req.query.clinic_id as string;
  if (!clinicId) {
    res.status(400).json({
      error: {
        message: 'clinic id must be provided',
      },
    });
    return;
  }

  if (!req.user) {
    res.status(401).json({ message: 'Unauthorized access' });
    return;
  }

  listCallsForAClinic(req.user, { clinicId: clinicId as string })
    .then((data) => {
      res.json({ data });
    })
    .catch((error: Error) => {
      const statusCode =
        (error as AxiosError).response?.status ??
        (error as HttpApiError).statusCode ??
        500;
      res.status(statusCode);
      const fallbackErrorResponse = getGenericErrorResponse();
      fallbackErrorResponse.error.message = error.message;
      res.json((error as AxiosError).response?.data ?? fallbackErrorResponse);
    });
});

export default router;

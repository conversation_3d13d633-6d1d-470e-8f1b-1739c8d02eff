import { Schema, model, Types, Document } from 'mongoose';
import { UserClinicRoleType } from './UserClinicRole';

export interface IUserInvite extends Document {
  invited_by: string;
  inviter_name: string;
  invitee_name: string;
  invitee_email: string;
  clinic_ids: string[];
  role: UserClinicRoleType;
  token: string;
  accepted: boolean;
  accepted_by?: string;
  expires_at: Date;
}

const UserInviteSchema = new Schema<IUserInvite>(
  {
    invited_by: {
      type: String,
      required: true,
    },
    inviter_name: {
      type: String,
      required: true,
    },
    invitee_name: {
      type: String,
      required: true,
    },
    invitee_email: {
      type: String,
      required: true,
    },
    clinic_ids: [
      {
        type: Types.ObjectId,
        ref: 'Clinic',
        required: true,
      },
    ],
    role: {
      type: String,
      enum: ['master', 'staff'],
      required: true,
    },
    token: {
      type: String, // invite_id in URL
      required: true,
      unique: true,
    },
    accepted: {
      type: Boolean,
      default: false,
    },
    accepted_by: {
      type: String, // User who accepted (after signup/login)
    },
    expires_at: {
      type: Date,
      default: () => new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days expiry
    },
  },
  {
    timestamps: true, // adds createdAt and updatedAt
  },
);

export const UserInvite = model('UserInvite', UserInviteSchema);

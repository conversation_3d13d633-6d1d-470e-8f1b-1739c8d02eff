import mongoose, { Schema, Document } from 'mongoose';

export type UserClinicRoleType = 'master' | 'staff';

export interface IUserClinicRole extends Document {
  user_id: string;
  clinic_id: string;
  role: UserClinicRoleType;
}

const UserClinicRoleSchema = new Schema<IUserClinicRole>({
  user_id: { type: String, required: true },
  clinic_id: { type: String, required: true },
  role: { type: String, enum: ['master', 'staff'], required: true },
});

// Prevent duplicate (user, clinic) entries
UserClinicRoleSchema.index({ user_id: 1, clinic_id: 1 }, { unique: true });

export const UserClinicRole = mongoose.model<IUserClinicRole>(
  'UserClinicRole',
  UserClinicRoleSchema,
);

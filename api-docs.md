## CURL to test Agent function calls

### book_appointment_for_new_patient

```
curl --location 'http://localhost:4000/api/agent/function-call' \
--header 'x-retell-signature: v=1744295628720,d=1a9a8d7c7eced2e8b700c7aa33fb34153e0cc165bf2df39362ef50b55dcbae50' \
--header 'Content-Type: application/json' \
--data '{
    "call": {
        "agent_id": "agent_508557a3dd8c20b18836a4dcba",
        "from_number": "+91**********",
        "to_number": "+***********",
        "direction": "inbound",
        "telephony_identifier": {
            "twilio_call_sid": "CAd41a92b4b18d1a6760a9703ea4365a5d"
        }
    },
    "name": "book_appointment_for_new_patient",
    "args": {
        "patient_mobile": "**********",
        "patient_first_name": "testF",
        "patient_last_name": "testL",
        "patient_dob": "1996-01-09",
        "practitioner_id": "1664574157834167650",
        "appointment_date": "2025-10-05T10:00:00+10:00",
        "reason_for_visit": "general consultation",
        "patient_address": "Gujarat, India"

    }
}'
```

### book_appointment_for_existing_patient

```
curl --location 'http://localhost:4000/api/agent/function-call' \
--header 'x-retell-signature: v=1745518391261,d=c5457f6ae3cf2ed7695598b31f8e4adc65eb7ac4b465680021a4c8e427ae9c54' \
--header 'Content-Type: application/json' \
--data '{
    "call": {
        "agent_id": "agent_508557a3dd8c20b18836a4dcba",
        "from_number": "+91**********",
        "to_number": "+***********",
        "direction": "inbound",
        "telephony_identifier": {
            "twilio_call_sid": "CAd41a92b4b18d1a6760a9703ea4365a5d"
        }
    },
    "name": "book_appointment_for_existing_patient",
    "args": {
        "patient_id":"1675418858938181109",
        "practitioner_id": "1675425145964996438",
        "appointment_date": "2025-10-06T11:00:00+05:30",
        "reason_for_visit": "general consultation1"
    }
}'
```

### reschedule_appointment

```
curl --location 'http://localhost:4000/api/agent/function-call' \
--header 'x-retell-signature: v=1745518391261,d=c5457f6ae3cf2ed7695598b31f8e4adc65eb7ac4b465680021a4c8e427ae9c54' \
--header 'Content-Type: application/json' \
--data '{
    "call": {
        "agent_id": "agent_508557a3dd8c20b18836a4dcba",
        "from_number": "+91**********",
        "to_number": "+***********",
        "direction": "inbound",
        "telephony_identifier": {
            "twilio_call_sid": "CAd41a92b4b18d1a6760a9703ea4365a5d"
        }
    },
    "name": "reschedule_appointment",
    "args": {
        "appointment_id": "1676866257447561100",
        "new_appointment_starts_at": "2025-10-05T10:00:00+05:30",
        "reschedule_reason": "general consultation",
        "new_appointment_ends_at":"2025-10-05T10:30:00+05:30"
    }
}'
```

### cancel_appointment

```
curl --location 'http://localhost:4000/api/agent/function-call' \
--header 'x-retell-signature: v=1745518391261,d=c5457f6ae3cf2ed7695598b31f8e4adc65eb7ac4b465680021a4c8e427ae9c54' \
--header 'Content-Type: application/json' \
--data '{
    "call": {
        "agent_id": "agent_508557a3dd8c20b18836a4dcba",
        "from_number": "+91**********",
        "to_number": "+***********",
        "direction": "inbound",
        "telephony_identifier": {
            "twilio_call_sid": "CAd41a92b4b18d1a6760a9703ea4365a5d"
        }
    },
    "name": "cancel_appointment",
    "args": {
        "appointment_id": "1675428626062910639",
        "cancellation_reason": "Feeling better"
    }
}'
```

### get_patient_details

```
curl --location 'http://localhost:4000/api/agent/function-call' \
--header 'x-retell-signature: v=1745518391261,d=c5457f6ae3cf2ed7695598b31f8e4adc65eb7ac4b465680021a4c8e427ae9c54' \
--header 'Content-Type: application/json' \
--data '{
    "call": {
        "agent_id": "agent_508557a3dd8c20b18836a4dcba",
        "from_number": "+91**********",
        "to_number": "+***********",
        "direction": "inbound",
        "telephony_identifier": {
            "twilio_call_sid": "CAd41a92b4b18d1a6760a9703ea4365a5d"
        }
    },
    "name": "get_patient_details",
    "args": {
        "patient_last_name": "testL"
    }
}'
```

### get_patient_appointment_details

```
curl --location 'http://localhost:4000/api/agent/function-call' \
--header 'x-retell-signature: v=1745518391261,d=c5457f6ae3cf2ed7695598b31f8e4adc65eb7ac4b465680021a4c8e427ae9c54' \
--header 'Content-Type: application/json' \
--data '{
    "call": {
        "agent_id": "agent_508557a3dd8c20b18836a4dcba",
        "from_number": "+91**********",
        "to_number": "+***********",
        "direction": "inbound",
        "telephony_identifier": {
            "twilio_call_sid": "CAd41a92b4b18d1a6760a9703ea4365a5d"
        }
    },
    "name": "get_patient_appointment_details",
    "args": {
        "patient_id": "1675434665021810178"
    }
}'
```

### get_practitioners_list

```
curl --location 'http://localhost:4000/api/agent/function-call' \
--header 'x-retell-signature: v=1745518391261,d=c5457f6ae3cf2ed7695598b31f8e4adc65eb7ac4b465680021a4c8e427ae9c54' \
--header 'Content-Type: application/json' \
--data '{
    "call": {
        "agent_id": "agent_508557a3dd8c20b18836a4dcba",
        "from_number": "+91**********",
        "to_number": "+***********",
        "direction": "inbound",
        "telephony_identifier": {
            "twilio_call_sid": "CAd41a92b4b18d1a6760a9703ea4365a5d"
        }
    },
    "name": "get_practitioners_list",
    "args": {
    }
}'
```

### update_patient_details

```
curl --location 'http://localhost:4000/api/agent/function-call' \
--header 'x-retell-signature: v=1745518391261,d=c5457f6ae3cf2ed7695598b31f8e4adc65eb7ac4b465680021a4c8e427ae9c54' \
--header 'Content-Type: application/json' \
--data '{
    "call": {
        "agent_id": "agent_508557a3dd8c20b18836a4dcba",
        "from_number": "+91**********",
        "to_number": "+***********",
        "direction": "inbound",
        "telephony_identifier": {
            "twilio_call_sid": "CAd41a92b4b18d1a6760a9703ea4365a5d"
        }
    },
    "name": "update_patient_details",
    "args": {
        "patient_id": "1675428619813398013",
        "patient_mobile":"**********"
    }
}'
```

### get_available_appointment_slots

```
curl --location 'http://localhost:4000/api/agent/function-call' \
--header 'x-retell-signature: v=1745518391261,d=c5457f6ae3cf2ed7695598b31f8e4adc65eb7ac4b465680021a4c8e427ae9c54' \
--header 'Content-Type: application/json' \
--data '{
    "call": {
        "agent_id": "agent_508557a3dd8c20b18836a4dcba",
        "from_number": "+91**********",
        "to_number": "+***********",
        "direction": "inbound",
        "telephony_identifier": {
            "twilio_call_sid": "CAd41a92b4b18d1a6760a9703ea4365a5d"
        }
    },
    "name": "get_available_appointment_slots",
    "args": {
        "practitioner_id": "1675425145964996438",
        "from_date":"2025-10-01T10:00:00+05:30",
        "to_date":"2025-10-10T10:00:00+05:30"
    }
}'
```

## For admin

### CURL to test Admin assigning twilio phone number to business

```
curl --location 'http://localhost:4000/api/admin/retell/assign-twilio-number' \
--header 'Content-Type: application/json' \
--header 'Authorization: Bearer TOKEN' \
--data '{
    "phone_number": "+918373052288",
    "clinic_id": "681da2e44eeb8a6eba7c9381"
}'
```

### CURL to test Admin updating twilio phone number

```
curl --location 'http://localhost:4000/api/admin/retell/update-phone-number' \
--header 'Content-Type: application/json' \
--header 'Authorization: Bearer TOKEN' \
--data '{
    "phone_number": "+918373052268",
    "clinic_id": "681dc528c9796f1199149597"
}'
```

### CURL to test Admin listing all phone numbers from twilio

```
curl --location 'http://localhost:4000/api/admin/twilio-phone-numbers' \
--header 'Content-Type: application/json' \
--header 'Authorization: Bearer TOKEN'
```

### CURL to test Admin creating another admin

```
curl --location 'http://localhost:4000/api/admin/register-admin' \
--header 'Content-Type: application/json' \
--header 'Authorization: Bearer TOKEN' \
--data '{
    "email": "rajdemodak1"
}'
```

### CURL to test Admin listing all clinics

```
curl --location 'http://localhost:4000/api/admin/clinics' \
--header 'Content-Type: application/json' \
--header 'Authorization: Bearer TOKEN'
```

### CURL to test Admin listing all users

```
curl --location 'http://localhost:4000/api/admin/users' \
--header 'Content-Type: application/json' \
--header 'Authorization: Bearer TOKEN'
```

### CURL to test Admin listing all admins

```
curl --location 'http://localhost:4000/api/admin/admins' \
--header 'Content-Type: application/json' \
--header 'Authorization: Bearer TOKEN'
```

### CURL to test Admin get call analytics for a clinic

```
curl --location 'http://localhost:4000/api/admin/analytics/list-calls?clinic_id=681da2e44eeb8a6eba7c9381' \
--header 'Content-Type: application/json' \
--header 'Authorization: Bearer TOKEN'
```

### CURL to test Admin creating/updation a clinic

#### to update provide the clinic id

```
curl --location 'http://localhost:4000/api/admin/clinic' \
--header 'Content-Type: application/json' \
--header 'Authorization: Bearer TOKEN' \
--data '{
    "clinic_name": "My GP Gungahlin",
    "clinic_address": "Gurgaon, India",
    "clinic_email" : "<EMAIL>",
    "human_transfer_destination_number":"+91**********",
    "smart_reception_phone_no":"+************",
    "clinic_phone":"+************"
}'
```

## for User

### CURL to test retell - get all knowledge base details

```
curl --location 'http://localhost:4000/api/retell/knowledge-bases?clinic_id=681dc528c9796f1199149597' \
--header 'Content-Type: application/json' \
--header 'Authorization: Bearer TOKEN' \
--data ''
```

### CURL to test retell - get a agent details

```
curl --location 'http://localhost:4000/api/retell/agent?clinic_id=681dc528c9796f1199149597' \
--header 'Content-Type: application/json' \
--header 'Authorization: Bearer TOKEN' \
--data ''
```

### CURL to test retell - delete a knowledge base

```
curl --location --request DELETE 'http://localhost:4000/api/retell/knowledge-bases/knowledge_base_4b7bd6414d3afccf?clinic_id=681dc528c9796f1199149597' \
--header 'Content-Type: application/json' \
--header 'Authorization: Bearer TOKEN' \
--data '{
    "knowledge_base_id":"knowledge_base_780292b14d231dcb"
}'
```

### CURL to test retell - get call analytics for a business

```
curl --location 'http://localhost:4000/api/retell/analytics/list-calls?clinic_id=681dc528c9796f1199149597' \
--header 'Content-Type: application/json' \
--header 'Authorization: Bearer TOKEN' \
--data ''
```

### CURL to test admin login

```
curl --location 'https://app.smartreception.ai/api/auth/admin/otp/send' \
--header 'Content-Type: application/json' \
--data-raw '{
    "email": "<EMAIL>"
}'
```

```
curl --location 'https://app.smartreception.ai/api/auth/admin/otp/verify' \
--header 'Content-Type: application/json' \
--data-raw '{
    "email": "<EMAIL>",
    "otp": "328123"
}'
```

### CURL to test user login/signup

```
curl --location 'https://app.smartreception.ai/api/auth/otp/send' \
--header 'Content-Type: application/json' \
--data-raw '{
    "email": "<EMAIL>"
}'
```

```
curl --location 'https://app.smartreception.ai/api/auth/otp/verify' \
--header 'Content-Type: application/json' \
--data-raw '{
    "email": "<EMAIL>",
    "otp": "328123"
}'
```

### Onboard a Clinic

```
curl --location 'https://app.smartreception.ai/api/db/clinics' \
--header 'Authorization: Bearer TOKEN' \
--header 'Content-Type: application/json' \
--data-raw '{
    "clinic_name": "ACT Neurology Centre",
    "clinic_email": "<EMAIL>",
    "clinic_website": "https://actneurology.com.au/",
    "crm_details": {
      "name": "CLINIKO",
      "auth_details": {
        "api_key":
          "CLINIKO_API_KEY"
      },
      "custom_fields": {
        "business_id": "1684108806834889941",
        "appointment_type_id": "1684108806004417595"
      }
    },
    "human_transfer_destination_number": "+91**********"
}'
```

### Create Voice Agent

```
curl --location --request POST 'https://app.smartreception.ai/api/retell/voice-agent?clinic_id=6822e9604b8b521b826a0528' \
--header 'Authorization: Bearer TOKEN'
```

### Upload Knowledge Base

```
curl --location 'https://app.smartreception.ai/api/retell/knowledge-bases?clinic_id=6822e9604b8b521b826a0528' \
--header 'Authorization: Bearer TOKEN' \
--header 'Content-Type: application/json' \
--data '{
    "urls": [
        "https://actneurology.com.au/"
    ],
    "texts": [
        {
            "title": "FAQs",
            "text": "Q: What are your hours?\nA: We’re open Monday to Friday, 8:30 AM to 5:00 PM. Saturdays by appointment.\n\nQ: Where are you located?\nA: We’re located at Suite 3 & 4, 17 Napier Close, Deakin ACT 2600, and Suite 18, 40 Mary Potter Circuit, Bruce, ACT 2617.\n\nQ: Who are the doctors there?\nA: We have Dr Patel, Dr Jozwik, Dr Sampaio, Dr Pal, Dr Sales, and many other excellent specialists.\n\nQ: Do you offer telehealth?\nA: Yes, we do offer telehealth for certain types of appointments. I can connect you with our team to check if your consultation is eligible.\n\nQ: What’s the cost?\nA: Costs can differ based on the service and your referral—some services are eligible for Medicare rebates. I can connect you with our team for more details.\n\nQ: Do you bulk bill?\nA: Some services may be Medicare-supported. We can confirm based on your referral. I can connect you with our team for more details.\n\nQ: Do I need a referral?\nA: Yes, for specialist consultations, a referral is required.\n\nQ: Is there parking?\nA: Yes, we have free and accessible parking available on-site and are also situated near public transport stations.\n\nQ: How do I get there by public transport?\nA: ACTION bus routes 3 and 934 stop nearby. They run between Woden and Belconnen via the city.\n\nQ: Is there a taxi service?\nA: There’s a taxi rank at the hospital and a public phone available to call a taxi.\n\nQ: Do you offer community transport?\nA: You might be eligible. You can learn more at transport.act.gov.au.\n\nQ: Can I get travel assistance?\nA: If you live in a rural area, visit iptaas.health.nsw.gov.au for help with travel and accommodation.\n\nQ: Do you do EEGs?\nA: Yes, we perform diagnostic EEGs and paediatric EEGs onsite."
        }
    ]
}'
```

### Get Clinic Details

```
curl --location 'http://localhost:4000/api/db/clinics/681da2e44eeb8a6eba7c9381' \
--header 'Authorization: Bearer TOKEN' \
--data ''
```

### Get all User clinic Details

```
curl --location 'https://app.smartreception.ai/api/db/clinics' \
--header 'Authorization: Bearer TOKEN'
```

### Update clinic details

```
curl --location 'https://app.smartreception.ai/api/db/clinics' \
--header 'Authorization: Bearer TOKEN' \
--header 'Content-Type: application/json' \
--data '{
    "_id": "6822e8a34b8b521b826a0521",
    "human_transfer_destination_number": "+91**********"
}'
```

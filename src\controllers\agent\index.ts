import { IClinic } from '../../models/Clinic';
import logger from '../../modules/logger';
import {
  bookAppointmentForExistingPatient,
  bookAppointmentForNewPatient,
  cancelAppointment,
  getAvailableAppointmentSlots,
  getPatientAppointmentDetails,
  getPatientDetails,
  getPractitionersList,
  getPatientReferralSource,
  rescheduleAppointment,
  updatePatientDetails,
} from '../../modules/orchestrator';
import { IApiResponse } from '../../types';
import {
  FunctionNames,
  IAgentFunctionCallPayload,
  IBookAppointmentForExistingPatientArgs,
  IBookAppointmentForNewPatientArgs,
  ICancelAppointmentArgs,
  IGetAvailableAppointmentSlotsArgs,
  IGetPatientAppointmentDetailsArgs,
  IGetPatientDetailsArgs,
  IGetPractitionersListArgs,
  IGetPatientReferralSourceArgs,
  IRescheduleAppointmentArgs,
  IUpdatePatientDetailsArgs,
} from '../../types/agent';
import { convertDateIntoAest } from '../../utils';
import { getClinicDetails } from '../clinic_controller';

export const handleFunctionCallFromAgent = async (params: {
  body: IAgentFunctionCallPayload;
  headers: Record<string, string>;
}): Promise<IApiResponse> => {
  const { body } = params;
  try {
    let data: object = {};
    const clinicDetails = await getClinicDetails({
      agentId: body.call?.agent_id,
    });
    if (!clinicDetails) {
      throw new Error('No clinic found matching this agent');
    }
    const args = body.args;
    switch (body.name) {
      case FunctionNames.book_appointment_for_new_patient: {
        data = await handleBookAppointmentForNewPatient(
          clinicDetails,
          args as IBookAppointmentForNewPatientArgs,
        );
        break;
      }
      case FunctionNames.book_appointment_for_existing_patient: {
        data = await handleBookAppointmentForExistingPatient(
          clinicDetails,
          args as IBookAppointmentForExistingPatientArgs,
        );
        break;
      }
      case FunctionNames.reschedule_appointment: {
        data = await handleRescheduleAppointment(
          clinicDetails,
          args as IRescheduleAppointmentArgs,
        );
        break;
      }
      case FunctionNames.cancel_appointment: {
        data = await handleCancelAppointment(
          clinicDetails,
          args as ICancelAppointmentArgs,
        );
        break;
      }
      case FunctionNames.get_patient_details: {
        data = await handleGetPatientDetails(
          clinicDetails,
          args as IGetPatientDetailsArgs,
        );
        break;
      }
      case FunctionNames.get_patient_appointment_details: {
        data = await handleGetPatientAppointmentDetails(
          clinicDetails,
          args as IGetPatientAppointmentDetailsArgs,
        );
        break;
      }
      case FunctionNames.update_patient_details: {
        data = await handleUpdatePatientDetails(
          clinicDetails,
          args as IUpdatePatientDetailsArgs,
        );
        break;
      }
      case FunctionNames.get_available_appointment_slots: {
        data = await handleGetAvailableAppointmentSlots(
          clinicDetails,
          args as IGetAvailableAppointmentSlotsArgs,
        );
        break;
      }
      case FunctionNames.get_practitioners_list: {
        data = await handleGetPractitionersList(
          clinicDetails,
          args as IGetPractitionersListArgs,
        );
        break;
      }
      case FunctionNames.get_patient_referral_source: {
        data = await handleGetPatientReferralSource(
          clinicDetails,
          args as IGetPatientReferralSourceArgs,
        );
        break;
      }
      case FunctionNames.get_current_date_time: {
        const now = new Date();

        data = {
          current_date: now.toString(), // System timezone
          current_date_utc: now.toUTCString(),
          current_date_aest: convertDateIntoAest(now),
        };
        break;
      }
      case FunctionNames.get_clinic_details: {
        // not returning all data so that agent doesn't get confused
        data = {
          diagnostic_services: clinicDetails?.diagnostic_services ?? [],
          clinic_addresses: clinicDetails?.clinic_addresses ?? [],
        };
        break;
      }
      default: {
        throw new Error(`Unhandled function call: ${body.name}`);
      }
    }
    return {
      success: true,
      data,
    };
  } catch (error) {
    logger.error(error);
    throw error;
  }
};

const handleBookAppointmentForNewPatient = async (
  businessDetails: Partial<IClinic>,
  args: IBookAppointmentForNewPatientArgs,
): Promise<object> => {
  return bookAppointmentForNewPatient(businessDetails, args);
};

const handleBookAppointmentForExistingPatient = async (
  businessDetails: Partial<IClinic>,
  args: IBookAppointmentForExistingPatientArgs,
): Promise<object> => {
  return bookAppointmentForExistingPatient(businessDetails, args);
};

const handleRescheduleAppointment = async (
  businessDetails: Partial<IClinic>,
  args: IRescheduleAppointmentArgs,
): Promise<object> => {
  return rescheduleAppointment(businessDetails, args);
};

const handleCancelAppointment = async (
  businessDetails: Partial<IClinic>,
  args: ICancelAppointmentArgs,
): Promise<object> => {
  return cancelAppointment(businessDetails, args);
};

const handleGetPatientDetails = async (
  businessDetails: Partial<IClinic>,
  args: IGetPatientDetailsArgs,
): Promise<object> => {
  return getPatientDetails(businessDetails, args);
};

const handleGetPatientAppointmentDetails = async (
  businessDetails: Partial<IClinic>,
  args: IGetPatientAppointmentDetailsArgs,
): Promise<object> => {
  return getPatientAppointmentDetails(businessDetails, args);
};

const handleUpdatePatientDetails = async (
  businessDetails: Partial<IClinic>,
  args: IUpdatePatientDetailsArgs,
): Promise<object> => {
  return updatePatientDetails(businessDetails, args);
};

const handleGetAvailableAppointmentSlots = async (
  businessDetails: Partial<IClinic>,
  args: IGetAvailableAppointmentSlotsArgs,
): Promise<object> => {
  return getAvailableAppointmentSlots(businessDetails, args);
};

const handleGetPractitionersList = async (
  businessDetails: Partial<IClinic>,
  args: IGetPractitionersListArgs,
): Promise<object> => {
  return getPractitionersList(businessDetails, args);
};

const handleGetPatientReferralSource = async (
  businessDetails: Partial<IClinic>,
  args: IGetPatientReferralSourceArgs,
): Promise<object> => {
  return getPatientReferralSource(businessDetails, args);
};

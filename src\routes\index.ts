import express, { type Request, type Response } from 'express';
import logger from '../modules/logger';
import { createOrUpdateRetellCall } from '../controllers/retell_call_controller';

const router = express.Router();

router.get('/ping', (req: Request, res: Response) => {
  res.json({
    succes: true,
    message: 'pong',
  });
});

router.post('/retell/webhook', async (req: Request, res: Response) => {
  const { event, call } = req.body;
  logger.info(`Retell Webhook: received event ${event}`);
  if (call?.call_id) {
    await createOrUpdateRetellCall(call, event);
  }
  // Acknowledge the receipt of the event
  res.status(204).send();
});

export default router;

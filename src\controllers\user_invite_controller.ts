import { UserClinicRole } from '../models/UserClinicRole';
import { IUserInvite, UserInvite } from '../models/UserInvite';
import { AdminPrivilegeLevel, IAuthUser } from '../types';
import HttpApiError from '../utils/http_api_error';
import { sendUserInviteEmail } from './email_controller';
import { getUserDetails } from './user';
import { nanoid } from 'nanoid';

// user here is inviter
export const createUserInvite = async (
  user: IAuthUser,
  payload: Partial<IUserInvite>,
): Promise<IUserInvite> => {
  if (
    !payload?.invitee_email ||
    !payload?.invitee_name ||
    !payload.role ||
    !payload.clinic_ids?.length
  ) {
    throw new HttpApiError(
      400,
      'Missing invitee_email/invitee_name/role/clinic_ids',
    );
  }
  const userDetails = await getUserDetails(user);

  if (user?.role === 'admin') {
    // for admin, only check privilege level
    if (
      !user?.admin?.privilege_level ||
      ![AdminPrivilegeLevel.OWNER, AdminPrivilegeLevel.EDITOR].includes(
        user?.admin?.privilege_level,
      )
    ) {
      throw new HttpApiError(
        403,
        'You do not have privilege for this operation',
      );
    }
  } else {
    const userClinicRolesPromises = payload.clinic_ids.map((id) =>
      UserClinicRole.findOne({
        user_id: user.uid,
        clinic_id: id,
      }),
    );
    const userClinicRolesResponses = await Promise.all(userClinicRolesPromises);
    for (const response of userClinicRolesResponses) {
      if (!response) {
        throw new HttpApiError(403, `Clinic not found`);
      }
      if (response?.role !== 'master') {
        throw new HttpApiError(
          403,
          `You do not have master permission for clinic ${response?.clinic_id}`,
        );
      }
    }
  }
  // create an entry in UserInvite
  const token = nanoid(24);

  const newInvite = await UserInvite.create({
    invited_by: user.uid,
    inviter_name: userDetails?.first_name,
    invitee_name: payload.invitee_name,
    invitee_email: payload.invitee_email.toLowerCase(),
    clinic_ids: payload.clinic_ids,
    role: payload.role,
    token,
    accepted: false,
    expires_at: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days
  });

  void sendUserInviteEmail(newInvite);

  return newInvite.toObject();
};

// user here is invitee
export const getUserInvite = async (
  user: IAuthUser,
  payload: { token: string },
): Promise<IUserInvite> => {
  if (!payload.token) {
    throw new HttpApiError(400, 'Token is required');
  }

  const invite = await UserInvite.findOne({ token: payload.token });
  const userDetails = await getUserDetails(user);

  if (!invite) {
    throw new HttpApiError(404, 'Invite not found');
  }

  if (invite.accepted) {
    throw new HttpApiError(400, 'Invite already accepted');
  }

  if (invite.expires_at < new Date()) {
    throw new HttpApiError(400, 'Invite has expired');
  }

  // Optional: confirm email match
  if (
    userDetails?.email?.toLowerCase() !== invite.invitee_email.toLowerCase()
  ) {
    throw new HttpApiError(403, 'Invite does not match your email');
  }

  return invite.toObject(); // strip Mongoose document
};

// user here is invitee
export const acceptUserInvite = async (
  user: IAuthUser,
  payload: { token: string },
): Promise<IUserInvite> => {
  if (!payload.token) {
    throw new HttpApiError(400, 'Token is required');
  }

  const invite = await UserInvite.findOne({ token: payload.token });
  const userDetails = await getUserDetails(user);

  if (!invite) {
    throw new HttpApiError(404, 'Invite not found');
  }

  if (invite.accepted) {
    throw new HttpApiError(400, 'Invite already accepted');
  }

  if (invite.expires_at < new Date()) {
    throw new HttpApiError(400, 'Invite has expired');
  }

  if (
    userDetails?.email?.toLowerCase() !== invite.invitee_email.toLowerCase()
  ) {
    throw new HttpApiError(403, 'Invite does not match your email');
  }

  // Assign role(s) to user for each clinic
  const newRoles = invite.clinic_ids.map((clinic_id) => ({
    user_id: user.uid,
    clinic_id,
    role: invite.role,
  }));

  await UserClinicRole.insertMany(newRoles);

  // Mark invite as accepted
  invite.accepted = true;
  await invite.save();

  return invite.toObject();
};

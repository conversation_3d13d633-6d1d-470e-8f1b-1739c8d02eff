// AIVA Agent can invoke these function depending upon Patient's intent
// any change made here should be refrelcetd in getLlmTools@controllers/retell/util.ts
export enum FunctionNames {
  book_appointment_for_new_patient = 'book_appointment_for_new_patient',
  book_appointment_for_existing_patient = 'book_appointment_for_existing_patient',
  reschedule_appointment = 'reschedule_appointment',
  cancel_appointment = 'cancel_appointment',
  get_patient_details = 'get_patient_details',
  get_patient_appointment_details = 'get_patient_appointment_details',
  update_patient_details = 'update_patient_details',
  get_available_appointment_slots = 'get_available_appointment_slots',
  get_practitioners_list = 'get_practitioners_list',
  get_patient_referral_source = 'get_patient_referral_source',
  get_current_date_time = 'get_current_date_time',
  get_clinic_details = 'get_clinic_details',
}

export interface IAgentFunctionCallPayload {
  call: {
    call_id: string;
    agent_id: string;
    transcript: string;
    from_number: string;
    to_number: string;
    direction: 'inbound';
    telephony_identifier: {
      twilio_call_sid: string;
    };
  };
  name: FunctionNames;
  args: FunctionArgs;
}

// any change made here should be refrelcetd in getLlmTools@controllers/retell/util.ts
export interface IBookAppointmentForNewPatientArgs {
  patient_mobile: string;
  patient_dob: string;
  patient_first_name: string;
  patient_last_name: string;
  patient_gender?: 'male' | 'female';
  patient_address?: string;
  reason_for_visit?: string;
  appointment_date: string;
  practitioner_id: string;
  // business id in CLINIKO
  business_location_id: string;
}

export interface IBookAppointmentForExistingPatientArgs {
  patient_id: string;
  reason_for_visit: string;
  appointment_date: string;
  practitioner_id: string;
  business_location_id: string;
}

export interface IRescheduleAppointmentArgs {
  appointment_id: string;
  new_appointment_starts_at: string; //changed
  reschedule_reason?: string;
  new_appointment_ends_at: string;
}

export interface ICancelAppointmentArgs {
  appointment_id: string;
  cancellation_reason?: string;
}

export interface IGetPatientDetailsArgs {
  // Search by 1 or more fields
  //patient_mobile?: string; // not supported in Cliniko
  patient_dob?: string;
  patient_first_name?: string;
  patient_last_name?: string;
}

export interface IGetPatientAppointmentDetailsArgs {
  patient_id: string;
}

export interface IUpdatePatientDetailsArgs {
  patient_id: string;
  patient_title?: string;
  patient_mobile?: string;
  patient_dob?: string;
  patient_first_name?: string;
  patient_last_name?: string;
}

export interface IGetAvailableAppointmentSlotsArgs {
  practitioner_id: string;
  from_date: string;
  to_date: string;
  business_location_id: string;
}

// eslint-disable-next-line @typescript-eslint/no-empty-object-type
export interface IGetPractitionersListArgs {
  // no args required
}

export interface IGetPatientReferralSourceArgs {
  patient_id: string;
}

// eslint-disable-next-line @typescript-eslint/no-empty-object-type
export interface IGetClinicDetailsArgs {
  // no args required
}

// Union type for all possible args
export type FunctionArgs =
  | IBookAppointmentForNewPatientArgs
  | IBookAppointmentForExistingPatientArgs
  | IRescheduleAppointmentArgs
  | ICancelAppointmentArgs
  | IGetPatientDetailsArgs
  | IGetPatientAppointmentDetailsArgs
  | IUpdatePatientDetailsArgs
  | IGetAvailableAppointmentSlotsArgs
  | IGetPractitionersListArgs
  | IGetPatientReferralSourceArgs
  | IGetClinicDetailsArgs;

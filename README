# Smart Reception Backend

## Initial setup

1. Initialize a node project `npm init`
2. Install typescript `yarn add -D typescript @types/express @types/node`
3. Initialize Typescript `npx tsc --init`
4. Add `.gitignore`
5. Edit generated `tsconfig.json` - `outDir`, `include`, `exclude`
6. Update `main` in package.json to `dist`
7. Create `src/index.ts`
8. Add compiler `yarn add -D nodemon ts-node`
9. Add eslint `yarn add -D eslint @typescript-eslint/parser @typescript-eslint/eslint-plugin typescript-eslint`
10. Add prettier `yarn add -D prettier` and `.prettierrc.js`
11. Precommit hook `npx mrm lint-staged`
12. Update package.json - `scripts` and `lint-staged` command

## How to Install

1. Clone the repo `git clone https://github.com/Smart-reception/smart-reception-be.git`
2. Install Dependencies `cd smart-reception-be && yarn install`
3. Run local server `yarn dev`

## CURL to test Agent function calls

```
curl --location 'http://localhost:4000/api/agent/function-call' \
--header 'x-retell-signature: v=1744295628720,d=1a9a8d7c7eced2e8b700c7aa33fb34153e0cc165bf2df39362ef50b55dcbae50' \
--header 'Content-Type: application/json' \
--data '{
    "call": {
        "agent_id": "agent_508557a3dd8c20b18836a4dcba",
        "from_number": "+91**********",
        "to_number": "+61251375424",
        "direction": "inbound",
        "telephony_identifier": {
            "twilio_call_sid": "CAd41a92b4b18d1a6760a9703ea4365a5d"
        }
    },
    "name": "book_appointment_for_new_patient",
    "args": {
        "patient_mobile": "**********",
        "patient_first_name": "testF",
        "patient_last_name": "testL",
        "patient_dob": "1996-01-09",
        "practitioner_id": "1664574157834167650",
        "appointment_date": "2025-10-05T10:00:00+10:00",
        "reason_for_visit": "general consultation",
        "patient_address": "Gujarat, India"

    }
}'
```


## Setting up ECS

### Necessary IAM permissions-

1. AmazonEC2ContainerRegistryFullAccess
2. AmazonEC2FullAccess
3. AmazonECS_FullAccess
4. AmazonECSTaskExecutionRolePolicy
5. AmazonRoute53FullAccess
6. AmazonS3FullAccess
7. CloudWatchLogsFullAccess
8. AWSCertificateManagerFullAccess
9. SecretsManagerReadWrite
10. IAMGetRole (Customer inline)

IAMGetRole Inline-

```
{
	"Version": "2012-10-17",
	"Statement": [
		{
			"Effect": "Allow",
			"Action": [
				"iam:CreateRole",
				"iam:GetRole",
				"iam:AttachRolePolicy",
        "iam:PutRolePolicy",
        "iam:ListPolicies",
        "iam:DetachRolePolicy",
        "iam:GetPolicy",
        "iam:GetPolicyVersion",
        "iam:ListServerCertificates",
        "access-analyzer:ListPolicyGenerations"
			],
			"Resource": "*"
		}
	]
}
```

### Create ECS Cluster-

1. Go to https://ap-southeast-2.console.aws.amazon.com/ecs/v2/clusters?region=ap-southeast-2
2. Create a Cluster "Smart-reception" - AWS Fargate (serverless), Opt in for Cloudwatch container insights

### Create ECR Repository-

1. Go to https://ap-southeast-2.console.aws.amazon.com/ecr/private-registry/repositories?region=ap-southeast-2
2. Create private repositories "smart-reception-be" & "smart-reception-fe" and enable Tag immutability

### Push Image to Repository-

1. Login to Docker

```
aws ecr get-login-password --region ap-southeast-2 --profile smart-reception | docker login --username AWS --password-stdin 341801066689.dkr.ecr.ap-southeast-2.amazonaws.com
```

You must have a profile declared in `~/.aws/credentials`

```
[smart-reception]
region=ap-southeast-2
aws_access_key_id=ACCESS_KEY_ID
aws_secret_access_key=SECRET_ACCESS_KEY
```

2. Build Image `docker build -t smart-reception-be .`

Here I am inside `smart-reception-be` repo(folder).

3. Tag this docker image

`docker tag smart-reception-be:latest 341801066689.dkr.ecr.ap-southeast-2.amazonaws.com/smart-reception-be:latest`

4. Push this image to ECR

`docker push 341801066689.dkr.ecr.ap-southeast-2.amazonaws.com/smart-reception-be:latest`


### Create Task definition

1. Go to https://ap-southeast-2.console.aws.amazon.com/ecs/v2/task-definitions?region=ap-southeast-2
2. Click on "create new task definition"
3. Task definition family - `smart-reception-be`
4. Choose `0.5vCPU` and `1 GB memory`
5. Container details- Name = `smart-reception-be`, Image URI = Copy it from ECR image
6. Port Mapping- 4000 (smart-reception-be), 3000 (smart-reception-fe)
7. Environment Variables: `PORT=4000/3000`, `NODE_ENV=production`
8. Leave other settings as it is and click on create

Note-

ecsTaskExecutionRolePolicy must have below permissions if using secret manager

```
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Effect": "Allow",
            "Action": [
                "secretsmanager:GetSecretValue"
            ],
            "Resource": [
                "arn:aws:secretsmanager:ap-southeast-2:341801066689:secret:prod/smart-reception-be-YqNw0e"
            ]
        }
    ]
}
```

### Create a VPC

1. Go to https://ap-southeast-2.console.aws.amazon.com/vpcconsole/home?region=ap-southeast-2#CreateVpc:createMode=vpcWithResources
2. Select "VPC and more"
3. Choose name "ADS VPC", ADS = Automated Deployment System
4. IPv4 CIDR block: 10.0.0.0/20
5. No of AZs: 2, No of Public Subnets: 2, No of Private Subnets: 0
6. Click on "Create VPC"

### Create a Load Balancer

1. Go to https://ap-southeast-2.console.aws.amazon.com/ec2/home?region=ap-southeast-2#LoadBalancers
2. Use Name: `smart-reception-public`, Scheme: `Internet-facing` and IP address type: `IPv4`
3. Select newly created `ADS VPC` and check all AZs
4. When selecting security group, click on "Create new"
5. Create a new security group- Name: `ADS-default-sg`, Description: `HTTP & HTTPS to ELB`, VPC: `ADS VPC`, Inbound rules for `HTTP` & `HTTPS` (anywhere)
6. Once this SG is created, go back to ELB creation in previous tab and choose this new security group + default one as well.
7. Listeners and routing: Click "Create target group", It'll open a new tab
8. Target Type: `IP Addresses`, Target group name: `smart-reception-be-service`, Protocol: `HTTP 80`, VPC: `ADS VPC`
9. Health checks: Path: `/api/ping`, Health check port: `Override, 4000`
10. Click "Next"
11. Specify IPs and define ports: Remove for now, ECS will do it automatically
12. Click "Create target group"
13. Go back to ELB page and select this newly created target group `smart-reception-be-service`
14. Click on "Create Load balancer"

### Create ECS Services

Create Security Group first-

1. Visit https://ap-southeast-2.console.aws.amazon.com/ec2/home?region=ap-southeast-2#SecurityGroups:
2. Click on "Create Security Group"
3. Name: `elb-ecs-fargate`
4. Description: `Allow all inbound connections but only from ELB`
5. VPC: `ADS VPC`
6. Inbound Rule: Custom TCP, source = `ADS-default-sg` Security Group
7. Click on "Create Security Group"

Create Service-

1. Visit https://ap-southeast-2.console.aws.amazon.com/ecs/v2/clusters/Smart-reception/create-service?region=ap-southeast-2
2. Select Task definition family- `smart-reception-be`
3. Service Name: `smart-reception-be-service`
4. VPC: `ADS VPC`
5. Security Group: `default` & `elb-ecs-fargate`
6. Load balancing: Application LB, Use an existing LB, Choose `smart-reception-public`
7. Listener: Use an existing listener, 80:HTTP
8. Use an existing target group: `smart-reception-be-service`
9. Click on "Create" button
10. http://smart-reception-public-96829716.ap-southeast-2.elb.amazonaws.com/api/ping should work now

### Enable HTTPS on ELB

1. Visit https://ap-southeast-2.console.aws.amazon.com/ec2/home?region=ap-southeast-2#LoadBalancers:
2. Check `smart-reception-public` ELB
3. Click on tab "Listeners and rules"
4. Click "Add Listeners"
5. Choose `HTTPS:443`
6. Forward to target group: `smart-reception-be-service`
7. Click on "Request new Certificate" from ACM, It will open in new tab
8. Choose "Request a public certificate"
9. FQDN: `*.smartreception.ai`
10. Click "Request"
11. From Domains section, Copy the CNAME entry to Cloudflare

```
Type: CNAME, Name: _68ad95e67353f3867fad424b862f935d.smartreception.ai., Target: _33d895143f74c229bb10682d4873103c.xlfgrmvvlj.acm-validations.aws., Proxy Status: DNS only
```

12. It'll take some time to show ths Certificate status as "Issued" from "Pending Validation"
13. Go back to "Add Listener" tab and select this certificate
14. Click on "Add" button
15. Modify the `HTTP:80` listener to redirect to URL (`Full URL`): `https://#{host}:443/#{path}?#{query}`, and Status Code: `301`
16. Click on "Save changes"
17. Make sure that `HTTP:80` does not have any other rules configured, delete if any and create same rule in `HTTPS:443`
18. HTTPS:443 rules-

```
Rule Condition Type: Path, Path Pattern is /api*, Forward to Target groups: smart-reception-be-service, Priority: 1

Default: Forwarding to smart-reception-be-service
```

### Map Domain Cloudflare <> ELB

1. Go to https://dash.cloudflare.com/bebed840d81ea4ef5189ddaaccd545c2/smartreception.ai/dns/records
2. Create these entries-

```
Type: CNAME, Name: ai, Content: smart-reception-public-96829716.ap-southeast-2.elb.amazonaws.com, Proxy Status: Proxied

Type: CNAME, Name: api, Content: smart-reception-public-96829716.ap-southeast-2.elb.amazonaws.com, Proxy Status: Proxied
```

### Setting up Github Actions

1. Visit https://github.com/smart-reception/smart-reception-be/actions
2. Delete any existing workflow
3. Select new workflow run `Deploy to Amazon ECS`
4. A new `aws.yml` with boilerplate code will be committed
5. Take `git pull` to get this file in local for editing
6. Edit this file replacing all variables
7. Create `ecs/task-definition.json` by downloading content from AWS
8. Configure secrets in Github organization/repository settings

### S3 Public Bucket policy

```
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Sid": "PublicReadGetObject",
            "Effect": "Allow",
            "Principal": "*",
            "Action": "s3:GetObject",
            "Resource": "arn:aws:s3:::smart-reception-public/*"
        }
    ]
}
```

### Retell Resources-

1. https://docs.retellai.com/api-references/create-agent
2. https://docs.retellai.com/api-references/create-retell-llm
3. https://www.npmjs.com/package/retell-sdk
4. https://www.twilio.com/docs/sip-trunking#configure-trunks
5. https://docs.retellai.com/deploy/twilio

### Twilio Elastic SIP Trunking details

1. Termination URI: `smartreception-ai.pstn.sydney.twilio.com`
2. Localised termination URI: `smartreception-ai.pstn.twilio.com`

Twilio: Use same credentials as existing numbers (username: <EMAIL>)
Retell: Use twilio-retell-cred-1 in credentials

### Amazon Document Db

1. Set proper permissions for EC2 private key `chmod 400 smart-reception-ec2-amazon-doc-db.pem`
2. Login to Ubuntu instance `ssh -i smart-reception-ec2-amazon-doc-db.pem ubuntu@************`


#### Installing mongosh on Ubuntu (Dev env)

https://www.mongodb.com/docs/manual/tutorial/install-mongodb-on-ubuntu/

1. Import the MongoDB GPG Key

```
sudo apt-get install gnupg curl
curl -fsSL https://www.mongodb.org/static/pgp/server-8.0.asc | \
   sudo gpg -o /usr/share/keyrings/mongodb-server-8.0.gpg \
   --dearmor
```

2. Add the MongoDB Repository

```
echo "deb [ arch=amd64,arm64 signed-by=/usr/share/keyrings/mongodb-server-8.0.gpg ] https://repo.mongodb.org/apt/ubuntu noble/mongodb-org/8.0 multiverse" | sudo tee /etc/apt/sources.list.d/mongodb-org-8.0.list
```

3.  Update Packages & Install mongosh

```
sudo apt-get update
sudo apt-get install -y mongodb-org
```

4. Verify Installation

```
mongosh -version
```

5. Start the mongod process 

```
sudo systemctl start mongod
```

6. Verify that MongoDB has started successfully.

```
sudo systemctl status mongod
```

7. Ensure that MongoDB will start following a system reboot 

```
sudo systemctl enable mongod
```

8. Stop & Restart

```
sudo systemctl stop mongod
sudo systemctl restart mongod
```

9. Setup Auth

- Login for very first time `mongosh`
- Create db `use smart_receptio`
- Create User

```
db.createUser({
  user: "USER",
  pwd: "PASSWORD",
  roles: [{ role: "readWrite", db: "smart_reception" }]
})
```
- `sudo nano /etc/mongod.conf`, add / uncomment

```
security:
  authorization: "enabled"
```

- Restart `sudo systemctl restart mongod`
- bind MongoDB to all interfaces: `/etc/mongod.conf`

```
net:
  bindIp: 0.0.0.0
```

#### MongoDB commands

1. Connect to shell `mongosh "********************************************************************"`
2. Switch database: `use smart_reception`
3. Drop Indexes `db.admins.dropIndex('mobile_1');`
4. Recreate an index `db.admins.createIndex({ mobile: 1 }, { unique: true, sparse: true });`
5. List collections `show collections;`
6. Drop collection: `db.clinics.drop()`
7. List all data `db.userclinicroles.find()`

#### Amazon Document DB Cluster Creation

1. Go to https://ap-southeast-2.console.aws.amazon.com/docdb/home?region=ap-southeast-2#subnetGroups
2. Click on Create Subnet Group CTA
3. Subnet group name `docdb-subnet-group`
4. Select ADS VPC and add at least two subnets in different Availability Zones for high availability.
5. Create a security group inside ADS-VPC with below details-
  - Name: docdb-ecs-fargate
  - Description: Connect to Amazon Document db on port 27017 from ECS
  - Inbound Rule: Custom TCP; 27017; Anywhere-IPv4
5. Go to https://ap-southeast-2.console.aws.amazon.com/docdb/home?region=ap-southeast-2#cluster-create  
7. Create Instance based Cluster within ADS-VPC and above security group

### Cliniko Resources

#### Online Booking Setup

1. Generate API key: https://help.cliniko.com/en/articles/1023957-generate-a-cliniko-api-key
2. Set Up Online Bookings-

To ensure the API returns available times, **online bookings must be enabled**, and the relevant **business**, **practitioner**, and **appointment types** must be configured. Follow the guide to set up online bookings here:
[Set Up Online Bookings](https://help.cliniko.com/en/articles/1023963-set-up-online-bookings)

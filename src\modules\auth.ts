import {
  CognitoAccessTokenPayload,
  CognitoIdTokenPayload,
} from 'aws-jwt-verify/jwt-model';
import {
  cognitoAccessTokenVerifier,
  cognitoIdTokenVerifier,
} from './cognito_verifier';

export const verifyCognitoAccessToken = async (
  token: string,
): Promise<CognitoAccessTokenPayload> => {
  const payload = await cognitoAccessTokenVerifier.verify(token);
  return payload;
};

export const verifyCognitoIdToken = async (
  token: string,
): Promise<CognitoIdTokenPayload> => {
  const payload = await cognitoIdTokenVerifier.verify(token);
  return payload;
};

const auth = {
  verifyCognitoAccessToken,
  verifyCognitoIdToken,
};

export default auth;

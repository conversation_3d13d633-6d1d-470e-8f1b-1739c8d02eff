import { Retell } from 'retell-sdk';
import { AgentCreateParams, AgentResponse } from 'retell-sdk/resources/agent';
import {
  LlmCreateParams,
  LlmResponse,
  LlmUpdateParams,
} from 'retell-sdk/resources/llm';
import {
  KnowledgeBaseCreateParams,
  KnowledgeBaseListResponse,
  KnowledgeBaseResponse,
} from 'retell-sdk/resources/knowledge-base';
import {
  PhoneNumberImportParams,
  PhoneNumberListResponse,
  PhoneNumberResponse,
  PhoneNumberUpdateParams,
} from 'retell-sdk/resources/phone-number';
import { DEV } from '../constants';
import { CallListParams, CallListResponse } from 'retell-sdk/resources/call';

const RETELL_API_KEY = process.env.RETELL_API_KEY ?? '';

export const isSignatureValid = (params: {
  body: object;
  signatureHeader: string;
  //rawBody: string
}) => {
  // bypass for local testing
  if (DEV) {
    return true;
  }
  const { body, signatureHeader } = params;
  return Retell.verify(JSON.stringify(body), RETELL_API_KEY, signatureHeader);
};

const retellClient = new Retell({
  apiKey: RETELL_API_KEY,
});

//llm
export const createRetellLlm = async (
  params: LlmCreateParams,
): Promise<LlmResponse> => {
  const llmResponse: LlmResponse = await retellClient.llm.create(params);
  return llmResponse;
};

export const updateRetellLlm = async (
  llm_id: string,
  updateParams: LlmUpdateParams,
): Promise<LlmResponse> => {
  const llmResponse: LlmResponse = await retellClient.llm.update(
    llm_id,
    updateParams,
  );
  return llmResponse;
};

export const getRetellLlmDetails = async (
  llm_id: string,
): Promise<LlmResponse> => {
  const llmResponse: LlmResponse = await retellClient.llm.retrieve(llm_id);
  return llmResponse;
};

export const createAgent = async (
  params: AgentCreateParams,
): Promise<AgentResponse> => {
  const agentResponse: AgentResponse = await retellClient.agent.create(params);
  return agentResponse;
};

export const getAgentDetails = async (
  agentId: string,
): Promise<AgentResponse> => {
  const agentResponse: AgentResponse =
    await retellClient.agent.retrieve(agentId);
  return agentResponse;
};

export const listAgents = async (): Promise<AgentResponse[]> => {
  const agentResponses: AgentResponse[] = await retellClient.agent.list();
  return agentResponses;
};

export const deleteAgent = async (agentId: string) => {
  return await retellClient.agent.delete(agentId);
};

export const updateAgent = async (
  agentId: string,
  params: Partial<AgentCreateParams>,
): Promise<AgentResponse> => {
  const agentResponse: AgentResponse = await retellClient.agent.update(
    agentId,
    params,
  );
  return agentResponse;
};

//knowledge-base
export const createKnowledgeBase = async (
  params: KnowledgeBaseCreateParams,
): Promise<KnowledgeBaseResponse> => {
  const knowledgeBaseResponse: KnowledgeBaseResponse =
    await retellClient.knowledgeBase.create(params);
  return knowledgeBaseResponse;
};

export const getKnowledgeBaseDetails = async (
  knowledge_base_id: string,
): Promise<KnowledgeBaseResponse> => {
  const knowledgeBaseResponse: KnowledgeBaseResponse =
    await retellClient.knowledgeBase.retrieve(knowledge_base_id);
  return knowledgeBaseResponse;
};

export const listKnowledgeBases =
  async (): Promise<KnowledgeBaseListResponse> => {
    const listOfKnowledgeBases: KnowledgeBaseListResponse =
      await retellClient.knowledgeBase.list();
    return listOfKnowledgeBases;
  };

export const deleteKnowledgeBase = async (knowledge_base_id: string) => {
  return await retellClient.knowledgeBase.delete(knowledge_base_id);
};

//phone-numbers
export const listPhoneNumbers = async (): Promise<PhoneNumberListResponse> => {
  const phoneNumberResponses: PhoneNumberListResponse =
    await retellClient.phoneNumber.list();
  return phoneNumberResponses;
};

export const importPhoneNumber = async (
  params: PhoneNumberImportParams,
): Promise<PhoneNumberResponse> => {
  const phoneNumberResponse: PhoneNumberResponse =
    await retellClient.phoneNumber.import(params);
  return phoneNumberResponse;
};

export const updatePhoneNumber = async (
  phoneNumber: string,
  params: PhoneNumberUpdateParams,
): Promise<PhoneNumberResponse> => {
  const phoneNumberResponse: PhoneNumberResponse =
    await retellClient.phoneNumber.update(phoneNumber, params);
  return phoneNumberResponse;
};

//call
export const listCalls = async (
  params: CallListParams,
): Promise<CallListResponse> => {
  const listCallsResponse: CallListResponse =
    await retellClient.call.list(params);
  return listCallsResponse;
};

export const getLlmDetailsFromAgentId = async (
  agentId: string,
): Promise<LlmResponse> => {
  const agentDetails: AgentResponse = await getAgentDetails(agentId);
  let llmId;
  if (agentDetails.response_engine.type === 'retell-llm') {
    llmId = agentDetails.response_engine.llm_id;
  }
  if (!llmId) {
    throw new Error('No LLM id found');
  }
  const llmResponse: LlmResponse = await getRetellLlmDetails(llmId);
  return llmResponse;
};

export const getPhoneNumberDetails = async (
  phoneNumber: string,
): Promise<PhoneNumberResponse> => {
  const phoneNumberResponse =
    await retellClient.phoneNumber.retrieve(phoneNumber);
  return phoneNumberResponse;
};

import { SESClient, SendEmailCommand } from '@aws-sdk/client-ses';
import type {
  SendEmailCommandInput,
  SendEmailCommandOutput,
} from '@aws-sdk/client-ses';

// Initialize the SES client
const sesClient = new SESClient({
  region: 'ap-southeast-2',
});

const DEFAULT_EMAIL_SOURCE = 'Smart Reception<<EMAIL>>';

/**
 * Send an email using SES.
 * @param params - Parameters for sending the email.
 * @returns The response from SES.
 */
export const sendEmail = async (
  params: SendEmailCommandInput,
): Promise<SendEmailCommandOutput> => {
  const command = new SendEmailCommand(params);
  return await sesClient.send(command);
};

/**
 * Send a simple email with HTML body.
 * @param params - Parameters for the email.
 * @returns The response from SES.
 */
export const sendSimpleEmail = async (params: {
  to: string[];
  htmlBody: string;
  subject: string;
}): Promise<SendEmailCommandOutput> => {
  const mailOptions: SendEmailCommandInput = {
    Destination: {
      ToAddresses: params.to,
    },
    Message: {
      Body: {
        Html: {
          Charset: 'UTF-8',
          Data: params.htmlBody,
        },
      },
      Subject: {
        Charset: 'UTF-8',
        Data: params.subject,
      },
    },
    Source: DEFAULT_EMAIL_SOURCE,
  };
  return await sendEmail(mailOptions);
};

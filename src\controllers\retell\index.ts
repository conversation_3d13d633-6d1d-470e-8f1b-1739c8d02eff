import { Agent<PERSON>reateParams, AgentResponse } from 'retell-sdk/resources/agent';
import logger from '../../modules/logger';
import {
  createAgent,
  createKnowledgeBase,
  createRetellLlm,
  deleteKnowledgeBase,
  getAgentDetails,
  getKnowledgeBaseDetails,
  getLlmDetailsFromAgentId,
  listCalls,
  updateAgent,
  updateRetellLlm,
} from '../../modules/retell';
import {
  ICreateKbPayload,
  ICreateVoiceAgentPayload,
  IDeleteKnowledgeBasePayload,
  IGetKnowledgeBasePayload,
} from '../../types/retell';
import {
  LlmCreateParams,
  LlmResponse,
  LlmUpdateParams,
} from 'retell-sdk/resources/llm';
import {
  KnowledgeBaseCreateParams,
  KnowledgeBaseResponse,
} from 'retell-sdk/resources/knowledge-base';
import { getClinicDetails } from '../clinic_controller';
import { Uploadable } from 'retell-sdk/uploads';
import fs from 'fs';
import { getLlmTools, getReceptionistPrompt } from './util';
import { CallListParams, CallListResponse } from 'retell-sdk/resources/call';
import { Clinic } from '../../models/Clinic';
import { IAuthUser } from '../../types';
import HttpApiError from '../../utils/http_api_error';
import {
  checkIfClinicUpdateIsAllowed,
  getParsedMobileObject,
  hasUserAccessToThisClinic,
} from '../../utils';

export const uploadAndMapKnowledgeBase = async (
  user: IAuthUser,
  params: ICreateKbPayload,
): Promise<object> => {
  logger.debug(params);

  await checkIfClinicUpdateIsAllowed(user, params.clinicId);

  try {
    const clinicDetails = await getClinicDetails({
      clinicId: params.clinicId,
    });
    if (!clinicDetails || !clinicDetails?.agent_id) {
      throw new HttpApiError(
        400,
        'This clinic does not exist or agent is not yet created',
      );
    }

    const uploadableFiles: Uploadable[] = params.files.map((file) => {
      return fs.createReadStream(file.path);
    });

    const knowledgeBaseParams: KnowledgeBaseCreateParams = {
      // max 40 characters, adjust in email not timestamp
      knowledge_base_name: `${(clinicDetails?.clinic_email ?? '').slice(0, 40 - (`${Date.now()}`.length + 1))}-${Date.now()}`,
      enable_auto_refresh: params.enableAutoRefresh ?? false,
      knowledge_base_files: uploadableFiles,
      knowledge_base_urls: params.urls,
      knowledge_base_texts: params.texts,
    };

    const knowledgeBaseResponse: KnowledgeBaseResponse =
      await createKnowledgeBase(knowledgeBaseParams);
    params.files.map(async (file) => {
      await fs.promises.unlink(file.path);
    });

    const llmResponse: LlmResponse = await getLlmDetailsFromAgentId(
      clinicDetails.agent_id,
    );
    const llmUpdateParams: LlmUpdateParams = {
      knowledge_base_ids: Array.from(
        new Set([
          ...(llmResponse.knowledge_base_ids ?? []),
          knowledgeBaseResponse.knowledge_base_id,
        ]),
      ),
    };
    await updateRetellLlm(llmResponse.llm_id, llmUpdateParams);
    return knowledgeBaseResponse;
  } catch (error) {
    logger.error(error);
    throw error;
  }
};

export const createVoiceAgent = async (
  user: IAuthUser,
  params: ICreateVoiceAgentPayload,
): Promise<object> => {
  logger.debug(params);

  await checkIfClinicUpdateIsAllowed(user, params.clinicId);

  try {
    const clinicDetails = await getClinicDetails({
      clinicId: params.clinicId,
    });
    if (!clinicDetails) {
      throw new HttpApiError(400, 'This clinic does not exist');
    }

    // Retell bug, it expects transferDestinationNumber to have a space between country code & number
    const transferDestinationNumberObj = getParsedMobileObject(
      clinicDetails.human_transfer_destination_number ?? '',
    );
    const transferDestinationNumber = `${transferDestinationNumberObj.country_code} ${transferDestinationNumberObj.number}`;
    if (clinicDetails.agent_id) {
      // case where agent id has been deleted (e.g. from Retell dashboard)
      try {
        const llmResponse: LlmResponse = await getLlmDetailsFromAgentId(
          clinicDetails.agent_id,
        );

        const llmUpdateParams: LlmUpdateParams = {
          general_prompt: getReceptionistPrompt(clinicDetails), //getReceptionistGeneralPrompt(),
          general_tools: getLlmTools({
            transferDestinationNumber,
          }),
          /*states: getReceptionistConversationNodes(),
          starting_state: 'start_node'*/
        };
        await updateRetellLlm(llmResponse.llm_id, llmUpdateParams);
        await updateAgent(clinicDetails.agent_id, {
          pronunciation_dictionary: params.pronunciationDict,
        });
        return await getAgentDetails(clinicDetails.agent_id);
      } catch (error) {
        logger.error(error);
      }
    }

    const llmCreationParams: LlmCreateParams = {
      model: 'gpt-4.1-mini',
      default_dynamic_variables: {
        clinic_name: clinicDetails.clinic_name ?? '',
      },
      general_prompt: getReceptionistPrompt(clinicDetails), //getReceptionistGeneralPrompt(),
      general_tools: getLlmTools({
        transferDestinationNumber,
      }),
      /*states: getReceptionistConversationNodes(),
      starting_state: 'start_node'*/
    };
    const llmResponse = await createRetellLlm(llmCreationParams);
    const agentCreationParams: AgentCreateParams = {
      response_engine: {
        llm_id: llmResponse.llm_id,
        type: 'retell-llm',
      },
      // Import this voice first from Retell UI
      // custom_voice_465d2a62fb4bf25abc3aa3dc1f -> Hannah (Female Australian Accent)
      voice_id: 'custom_voice_c2095e295634dc56c2b8ebe3be', // Anna (Female Australian Accent)
      agent_name: `${clinicDetails.clinic_email}-${Date.now()}`,
      pronunciation_dictionary: params.pronunciationDict,
      language: 'en-AU',
    };
    const agentResponse: AgentResponse = await createAgent(agentCreationParams);

    await Clinic.findByIdAndUpdate(clinicDetails._id, {
      $set: { agent_id: agentResponse.agent_id },
    });
    return agentResponse;
  } catch (error) {
    //console.dir(error, { depth: null })
    logger.error(error);
    throw error;
  }
};

export const getClinicAgentDetails = async (
  user: IAuthUser,
  params: IGetKnowledgeBasePayload,
): Promise<AgentResponse> => {
  logger.debug(params);

  if (user?.role === 'admin') {
    // any admin can perform GET operation
  } else {
    if (!(await hasUserAccessToThisClinic(user, params.clinicId))) {
      throw new HttpApiError(403, 'You do not have access to this clinic ');
    }
  }

  try {
    const clinicDetails = await getClinicDetails({
      clinicId: params.clinicId,
    });
    if (!clinicDetails || !clinicDetails?.agent_id) {
      throw new HttpApiError(
        400,
        'This clinic does not exist or agent is not yet created',
      );
    }
    const agentDetails: AgentResponse = await getAgentDetails(
      clinicDetails.agent_id,
    );
    return agentDetails;
  } catch (error) {
    logger.error(error);
    throw error;
  }
};

export const getAllKnowledgeBases = async (
  user: IAuthUser,
  params: IGetKnowledgeBasePayload,
): Promise<KnowledgeBaseResponse[]> => {
  logger.debug(params);

  if (user?.role === 'admin') {
    // any admin can perform GET operation
  } else {
    if (!(await hasUserAccessToThisClinic(user, params.clinicId))) {
      throw new HttpApiError(403, 'You do not have access to this clinic ');
    }
  }

  try {
    const clinicDetails = await getClinicDetails({
      clinicId: params.clinicId,
    });
    if (!clinicDetails || !clinicDetails?.agent_id) {
      throw new HttpApiError(
        400,
        'This clinic does not exist or agent is not yet created',
      );
    }
    const llmResponse: LlmResponse = await getLlmDetailsFromAgentId(
      clinicDetails.agent_id,
    );
    const knowledgeBaseIds: string[] = llmResponse?.knowledge_base_ids ?? [];
    const knowledgeBaseDetails: KnowledgeBaseResponse[] = await Promise.all(
      knowledgeBaseIds.map(
        async (knowledgeBaseId) =>
          await getKnowledgeBaseDetails(knowledgeBaseId),
      ),
    );
    return knowledgeBaseDetails;
  } catch (error) {
    logger.error(error);
    throw error;
  }
};

export const deleteOneKnowledgeBase = async (
  user: IAuthUser,
  params: IDeleteKnowledgeBasePayload,
): Promise<void> => {
  logger.debug(params);

  await checkIfClinicUpdateIsAllowed(user, params.clinicId);

  try {
    const clinicDetails = await getClinicDetails({
      clinicId: params.clinicId,
    });
    if (!clinicDetails || !clinicDetails?.agent_id) {
      throw new HttpApiError(
        400,
        'This clinic does not exist or agent is not yet created',
      );
    }
    const llmResponse: LlmResponse = await getLlmDetailsFromAgentId(
      clinicDetails.agent_id,
    );
    const knowledgeBaseIds: string[] = llmResponse?.knowledge_base_ids ?? [];

    // check if user has access to this Kb
    if (!knowledgeBaseIds.includes(params.knowledgeBaseId)) {
      throw new HttpApiError(400, 'You do not have access to this Knowledge');
    }
    const response = await deleteKnowledgeBase(params.knowledgeBaseId);
    return response;
  } catch (error) {
    logger.error(error);
    throw error;
  }
};

// deprecated, use only for debugging
export const listCallsForAClinic = async (
  user: IAuthUser,
  params: {
    clinicId: string;
  },
): Promise<CallListResponse> => {
  logger.debug(params);

  if (!(await hasUserAccessToThisClinic(user, params.clinicId))) {
    throw new HttpApiError(403, 'You do not have access to this clinic ');
  }

  try {
    const clinicDetails = await getClinicDetails({
      clinicId: params.clinicId,
    });
    if (!clinicDetails || !clinicDetails?.agent_id) {
      throw new HttpApiError(
        400,
        'This clinic does not exist or agent is not yet created',
      );
    }

    const listCallsParams: CallListParams = {
      filter_criteria: {
        agent_id: [clinicDetails?.agent_id],
      },
    };
    const listCallsResponse: CallListResponse =
      await listCalls(listCallsParams);
    return listCallsResponse;
  } catch (error) {
    logger.error(error);
    throw error;
  }
};

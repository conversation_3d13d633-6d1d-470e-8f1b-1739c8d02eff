import { Request } from 'express';

export interface IGenericErrorResponse {
  error: {
    message: string;
  };
}

// standard API response
export interface IApiResponse {
  success: boolean;
  data?: object;
  error?: {
    message: string;
  };
}

export enum CrmSystems {
  CLINIKO = 'CLINIKO',
}

export enum AdminPrivilegeLevel {
  VIEWER = 'viewer',
  EDITOR = 'editor',
  OWNER = 'owner',
}

export interface IAuthUser {
  uid: string;
  // role = admin -> Smart Reception internal employees
  // for Clinic admins, there is UserClinicRole schema
  role: 'admin' | 'user';
  admin?: {
    privilege_level: AdminPrivilegeLevel;
  };
}

export interface IApiRequest extends Request {
  user?: IAuthUser;
}

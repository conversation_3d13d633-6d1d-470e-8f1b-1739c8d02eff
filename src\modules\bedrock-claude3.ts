import {
  BedrockRuntimeClient,
  ConversationRole,
  ConverseCommand,
} from '@aws-sdk/client-bedrock-runtime';
import logger from './logger';

const client = new BedrockRuntimeClient({ region: 'ap-southeast-2' });

const modelId = 'anthropic.claude-3-haiku-20240307-v1:0';

export const getResponseFromClaude3 = async (
  prompt: string,
): Promise<object> => {
  const conversation = [
    {
      role: ConversationRole.USER,
      content: [{ text: prompt }],
    },
  ];

  const command = new ConverseCommand({
    modelId,
    messages: conversation,
    inferenceConfig: {
      maxTokens: 512,
      temperature: 0.2,
      topP: 0.9,
    },
  });

  const response = await client.send(command);
  const responseText = response?.output?.message?.content?.[0].text ?? '{}';
  logger.info(response?.output);
  // Parse the Claude response assuming it's a JSON string
  return JSON.parse(responseText);
};

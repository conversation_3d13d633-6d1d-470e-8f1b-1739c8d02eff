import {
  CognitoIdentityProviderClient,
  AdminAddUserToGroupCommand,
  AdminUpdateUserAttributesCommand,
  AdminGetUserCommand,
  ListUsersInGroupCommand,
  ListUsersCommand,
  UserType,
  AdminRemoveUserFromGroupCommand,
} from '@aws-sdk/client-cognito-identity-provider';
import { AdminPrivilegeLevel } from '../types';

const client = new CognitoIdentityProviderClient({
  region: process.env.AWS_REGION,
});

const userPoolId = process.env.COGNITO_USER_POOL_ID ?? '';

export const addUserToGroup = async (
  username: string,
  groupName: string,
): Promise<void> => {
  await client.send(
    new AdminAddUserToGroupCommand({
      GroupName: groupName,
      Username: username,
      UserPoolId: userPoolId,
    }),
  );
};

export const removeUserFromGroup = async (
  username: string,
  groupName: string,
): Promise<void> => {
  await client.send(
    new AdminRemoveUserFromGroupCommand({
      GroupName: groupName,
      Username: username,
      UserPoolId: userPoolId,
    }),
  );
};

export const updateUserAttributes = async (
  username: string,
  attributes: Record<string, string>,
): Promise<void> => {
  const userAttributes = Object.entries(attributes).map(([Name, Value]) => ({
    Name,
    Value,
  }));

  await client.send(
    new AdminUpdateUserAttributesCommand({
      Username: username,
      UserPoolId: userPoolId,
      UserAttributes: userAttributes,
    }),
  );
};

export const getUserFromCognito = async (username: string): Promise<object> => {
  const response = await client.send(
    new AdminGetUserCommand({
      Username: username,
      UserPoolId: userPoolId,
    }),
  );

  const attributes: Record<string, string> = {};
  for (const attr of response.UserAttributes ?? []) {
    if (attr.Name && attr.Value) {
      attributes[attr.Name] = attr.Value;
    }
  }

  return {
    username: response.Username,
    enabled: response.Enabled,
    user_status: response.UserStatus,
    attributes,
  };
};

export const getUsersInGroup = async (
  groupName: string,
): Promise<
  {
    uid: string;
    admin_level?: AdminPrivilegeLevel;
  }[]
> => {
  const res = await client.send(
    new ListUsersInGroupCommand({
      UserPoolId: userPoolId,
      GroupName: groupName,
      Limit: 50,
    }),
  );

  return (res.Users || []).map((u) => {
    const attr = Object.fromEntries(
      (u.Attributes || []).map((a) => [a.Name, a.Value]),
    );

    const rawPrivilege = attr['custom:admin_level'] as string;

    const privilegeLevel =
      rawPrivilege &&
      Object.values(AdminPrivilegeLevel).includes(
        rawPrivilege as AdminPrivilegeLevel,
      )
        ? (rawPrivilege as AdminPrivilegeLevel)
        : undefined;
    return {
      uid: u.Username || '',
      admin_level: privilegeLevel,
    };
  });
};

export const findUserByEmail = async (
  email: string,
): Promise<UserType | null> => {
  const response = await client.send(
    new ListUsersCommand({
      UserPoolId: userPoolId,
      Filter: `email = "${email}"`,
      Limit: 1,
    }),
  );
  return response.Users?.[0] ?? null;
};

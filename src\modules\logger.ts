import bunyan from 'bunyan';

import { DEV } from '../constants';

const streams = [
  {
    level: DEV ? bunyan.DEBUG : bunyan.INFO,
    stream: process.stdout,
  },
];

/**
 * Log levels- fatal(60), error(50), warn(40), info(30), debug(20), trace(10)
 * Setting a logger instance (or one of its streams) to a particular level
 * implies that all log records at that level and above are logged.
 * E.g. a logger set to level "info" will log records at level info
 * and above (warn, error, fatal).
 */
const logger = bunyan.createLogger({
  name: 'smartreception-be',
  streams,
  serializers: bunyan.stdSerializers,
});

export default logger;
